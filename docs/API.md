# <PERSON><PERSON>e AI CEO Agent - API Documentation

This document provides comprehensive API documentation for integrating with <PERSON><PERSON>e AI CEO Agent.

## 🌐 Base URL

- **Development**: `http://localhost:8000`
- **Production**: `https://your-domain.com`

All API endpoints are prefixed with `/api/v1`

## 🔐 Authentication

Currently, <PERSON><PERSON><PERSON> uses basic authentication. Future versions will include:
- JWT tokens
- API keys
- OAuth 2.0

## 📋 Task Management API

### Create Task

**POST** `/api/v1/tasks/`

Create a new task with automatic Eisenhower Matrix prioritization.

```json
{
  "title": "Q4 Budget Review",
  "description": "Complete quarterly budget analysis and recommendations",
  "urgency": "high",
  "importance": "high",
  "assignee": "Finance Team",
  "estimated_effort": 8.0,
  "deadline": "2024-01-15T17:00:00Z",
  "business_impact": "Critical for Q4 planning and resource allocation",
  "tags": ["finance", "quarterly", "budget"]
}
```

**Response:**
```json
{
  "id": 123,
  "uuid": "550e8400-e29b-41d4-a716-************",
  "title": "Q4 Budget Review",
  "priority_category": "DO_FIRST",
  "priority_score": 95.5,
  "urgency": "high",
  "importance": "high",
  "status": "pending",
  "is_overdue": false,
  "completion_percentage": 0.0,
  "created_at": "2024-01-10T10:00:00Z"
}
```

### Get Tasks

**GET** `/api/v1/tasks/`

Retrieve tasks with optional filtering.

**Query Parameters:**
- `skip` (int): Number of tasks to skip (pagination)
- `limit` (int): Maximum tasks to return (max 1000)
- `priority` (string): Filter by priority category
- `status` (string): Filter by task status
- `assignee` (string): Filter by assignee name

**Example:**
```
GET /api/v1/tasks/?priority=DO_FIRST&limit=10
```

### Get Priority Matrix

**GET** `/api/v1/tasks/matrix/priority`

Get tasks organized by Eisenhower Matrix categories.

**Response:**
```json
{
  "do_first": [
    {
      "id": 123,
      "title": "Critical Bug Fix",
      "priority_score": 98.0,
      "urgency": "critical",
      "importance": "critical"
    }
  ],
  "schedule": [...],
  "delegate": [...],
  "dont_do": [...]
}
```

### Get Daily Summary

**GET** `/api/v1/tasks/summary/daily`

Generate comprehensive daily executive summary.

**Response:**
```json
{
  "date": "2024-01-15T00:00:00Z",
  "stats": {
    "total_tasks": 24,
    "by_status": {
      "pending": 8,
      "in_progress": 12,
      "completed": 4
    },
    "by_priority": {
      "DO_FIRST": 3,
      "SCHEDULE": 8,
      "DELEGATE": 10,
      "DONT_DO": 3
    },
    "overdue_count": 2,
    "completed_today": 4
  },
  "key_insights": [
    "3 critical tasks require immediate attention",
    "Team productivity increased 15% this week"
  ],
  "recommended_actions": [
    "Prioritize overdue tasks",
    "Review resource allocation"
  ]
}
```

### Update Task

**PUT** `/api/v1/tasks/{task_id}`

Update task with automatic re-prioritization.

### Delete Task

**DELETE** `/api/v1/tasks/{task_id}`

Delete a task.

### Assign Task

**POST** `/api/v1/tasks/{task_id}/assign`

Assign task to team member.

```json
{
  "assignee": "John Doe",
  "deadline": "2024-01-20T17:00:00Z"
}
```

## 🤖 AI Communication API

### Process Query

**POST** `/api/v1/chat/query`

Process natural language strategic queries.

```json
{
  "query": "What are my top priority tasks for this week?",
  "context": {
    "department": "engineering",
    "focus_area": "product_launch"
  }
}
```

**Response:**
```json
{
  "query": "What are my top priority tasks for this week?",
  "response": {
    "content": "Based on your current task portfolio, here are your top priorities...",
    "type": "task_related",
    "has_recommendations": true
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "model": "gpt-4-turbo-preview"
}
```

### Get Daily Briefing

**GET** `/api/v1/chat/briefing`

Get AI-generated daily executive briefing.

### Analyze Priorities

**POST** `/api/v1/chat/analyze-priorities`

Get AI analysis of current task priorities.

### Generate Report

**POST** `/api/v1/chat/generate-report`

Generate executive reports with AI insights.

```json
{
  "report_type": "daily_summary",
  "time_period": "week",
  "include_market_data": false
}
```

## 🔗 Data Integration API

### Get Integration Status

**GET** `/api/v1/integrations/status`

Check health of all data integrations.

**Response:**
```json
{
  "timestamp": "2024-01-15T10:00:00Z",
  "connectors": {
    "jira": true,
    "slack": false,
    "alpha_vantage": true
  },
  "total_connectors": 3,
  "healthy_connectors": 2
}
```

### Sync from Jira

**POST** `/api/v1/integrations/sync/jira`

Sync tasks from Jira system.

### Sync from Slack

**POST** `/api/v1/integrations/sync/slack`

Extract tasks from Slack messages.

```json
{
  "channel_id": "C1234567890"
}
```

### Sync All Sources

**POST** `/api/v1/integrations/sync/all`

Run comprehensive sync across all sources.

### Get Market Intelligence

**GET** `/api/v1/integrations/market-intelligence`

Fetch latest market intelligence data.

### Test Connector

**POST** `/api/v1/integrations/test/{connector_name}`

Test specific data connector health.

## 📊 Strategic Analysis API

### Get Strategic Overview

**GET** `/api/v1/strategic/`

Get high-level strategic analysis overview.

### Get KPI Analysis

**GET** `/api/v1/strategic/kpis`

Analyze key performance indicators.

### Get SWOT Analysis

**GET** `/api/v1/strategic/swot`

Generate SWOT analysis.

### Get Market Analysis

**GET** `/api/v1/strategic/market`

Get market intelligence and competitive analysis.

## 🎯 Scenario Simulation API

### Get Scenarios Overview

**GET** `/api/v1/scenarios/`

Get available scenario simulation types.

### Simulate Resource Allocation

**POST** `/api/v1/scenarios/resource-allocation`

Model resource allocation scenarios.

```json
{
  "scenario_name": "Q2 Team Expansion",
  "parameters": {
    "additional_headcount": 5,
    "budget_increase": 150000,
    "timeline_months": 6
  }
}
```

### Simulate Market Strategy

**POST** `/api/v1/scenarios/market-strategy`

Model market expansion scenarios.

### Simulate Financial Planning

**POST** `/api/v1/scenarios/financial-planning`

Model financial planning scenarios.

## 📈 Response Formats

### Standard Response

All API responses follow this structure:

```json
{
  "data": {...},
  "timestamp": "2024-01-15T10:00:00Z",
  "status": "success"
}
```

### Error Response

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid task priority level",
    "details": {
      "field": "priority",
      "allowed_values": ["low", "medium", "high", "critical"]
    }
  },
  "timestamp": "2024-01-15T10:00:00Z",
  "status": "error"
}
```

## 🔄 Status Codes

- **200**: Success
- **201**: Created
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **422**: Validation Error
- **500**: Internal Server Error
- **503**: Service Unavailable

## 📝 Data Models

### Task Model

```json
{
  "id": 123,
  "uuid": "550e8400-e29b-41d4-a716-************",
  "title": "string",
  "description": "string",
  "priority_category": "DO_FIRST|SCHEDULE|DELEGATE|DONT_DO",
  "urgency": "low|medium|high|critical",
  "importance": "low|medium|high|critical",
  "priority_score": 95.5,
  "status": "pending|in_progress|completed|cancelled|on_hold",
  "assignee": "string",
  "estimated_effort": 8.0,
  "actual_effort": 6.5,
  "deadline": "2024-01-15T17:00:00Z",
  "created_at": "2024-01-10T10:00:00Z",
  "updated_at": "2024-01-12T14:30:00Z",
  "business_impact": "string",
  "tags": ["string"],
  "is_overdue": false,
  "days_until_deadline": 5,
  "completion_percentage": 75.0
}
```

## 🚀 Rate Limiting

- **Default**: 60 requests per minute
- **Burst**: 10 requests per second
- **Headers**: 
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset timestamp

## 🔧 SDK Examples

### Python

```python
import requests

# Create task
task_data = {
    "title": "Strategic Planning Session",
    "urgency": "high",
    "importance": "high"
}

response = requests.post(
    "http://localhost:8000/api/v1/tasks/",
    json=task_data
)

task = response.json()
print(f"Created task: {task['title']}")
```

### JavaScript

```javascript
// Get daily summary
const response = await fetch('/api/v1/tasks/summary/daily');
const summary = await response.json();

console.log(`Total tasks: ${summary.stats.total_tasks}`);
```

### cURL

```bash
# Process AI query
curl -X POST "http://localhost:8000/api/v1/chat/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What should I focus on today?",
    "context": {}
  }'
```

## 📚 Interactive Documentation

Visit `/docs` for interactive API documentation with:
- Live API testing
- Request/response examples
- Schema validation
- Authentication testing
