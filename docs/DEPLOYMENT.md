# Rynne AI CEO Agent - Deployment Guide

This guide provides comprehensive instructions for deploying <PERSON><PERSON><PERSON> AI CEO Agent in various environments.

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- OpenAI API key
- 4GB+ RAM recommended
- 10GB+ disk space

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd rynne
chmod +x setup.sh
./setup.sh
```

The setup script will:
- Create `.env` file from template
- Generate secure keys
- Build and start all services
- Verify deployment

### 2. Configure Environment

Edit `.env` file with your settings:

```bash
# Required
OPENAI_API_KEY=your_openai_api_key_here
SECRET_KEY=auto_generated_secure_key
JWT_SECRET_KEY=auto_generated_jwt_key

# Optional Integrations
JIRA_API_TOKEN=your_jira_token
SLACK_BOT_TOKEN=your_slack_token
ALPHA_VANTAGE_API_KEY=your_market_data_key
NEWS_API_KEY=your_news_api_key
```

### 3. Access Rynne

- **Frontend**: http://localhost:3000
- **API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Load Balancer (Nginx)                   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Frontend (React)                         │
│                 http://localhost:3000                      │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  Backend API (FastAPI)                     │
│                 http://localhost:8000                      │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   PostgreSQL    │ │      Redis      │ │   External APIs ││
│  │   :5432         │ │     :6379       │ │   (OpenAI, etc) ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 🐳 Docker Deployment

### Development Environment

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production Environment

```bash
# Use production profile
docker-compose --profile production up -d

# Scale services
docker-compose up -d --scale backend=3
```

## ☁️ Cloud Deployment

### AWS Deployment

#### Using ECS (Recommended)

1. **Create ECS Cluster**
```bash
aws ecs create-cluster --cluster-name rynne-cluster
```

2. **Build and Push Images**
```bash
# Build images
docker build -t rynne-backend ./backend
docker build -t rynne-frontend ./frontend

# Tag for ECR
docker tag rynne-backend:latest 123456789012.dkr.ecr.us-west-2.amazonaws.com/rynne-backend:latest
docker tag rynne-frontend:latest 123456789012.dkr.ecr.us-west-2.amazonaws.com/rynne-frontend:latest

# Push to ECR
docker push 123456789012.dkr.ecr.us-west-2.amazonaws.com/rynne-backend:latest
docker push 123456789012.dkr.ecr.us-west-2.amazonaws.com/rynne-frontend:latest
```

3. **Create Task Definitions**
```json
{
  "family": "rynne-backend",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "containerDefinitions": [
    {
      "name": "rynne-backend",
      "image": "123456789012.dkr.ecr.us-west-2.amazonaws.com/rynne-backend:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "DATABASE_URL",
          "value": "****************************************/rynne"
        }
      ]
    }
  ]
}
```

#### Using EC2

1. **Launch EC2 Instance**
   - Use Amazon Linux 2
   - t3.medium or larger
   - Security group: ports 80, 443, 22

2. **Install Docker**
```bash
sudo yum update -y
sudo yum install -y docker
sudo service docker start
sudo usermod -a -G docker ec2-user
```

3. **Deploy Application**
```bash
git clone <repository-url>
cd rynne
./setup.sh
```

### Google Cloud Platform

#### Using Cloud Run

1. **Build and Deploy Backend**
```bash
gcloud builds submit --tag gcr.io/PROJECT-ID/rynne-backend ./backend
gcloud run deploy rynne-backend --image gcr.io/PROJECT-ID/rynne-backend --platform managed
```

2. **Build and Deploy Frontend**
```bash
gcloud builds submit --tag gcr.io/PROJECT-ID/rynne-frontend ./frontend
gcloud run deploy rynne-frontend --image gcr.io/PROJECT-ID/rynne-frontend --platform managed
```

### Azure Deployment

#### Using Container Instances

```bash
# Create resource group
az group create --name rynne-rg --location eastus

# Deploy backend
az container create \
  --resource-group rynne-rg \
  --name rynne-backend \
  --image rynne-backend:latest \
  --ports 8000 \
  --environment-variables OPENAI_API_KEY=your-key

# Deploy frontend
az container create \
  --resource-group rynne-rg \
  --name rynne-frontend \
  --image rynne-frontend:latest \
  --ports 3000
```

## 🔧 Configuration

### Environment Variables

| Variable | Required | Description | Default |
|----------|----------|-------------|---------|
| `OPENAI_API_KEY` | Yes | OpenAI API key for AI features | - |
| `DATABASE_URL` | Yes | PostgreSQL connection string | - |
| `REDIS_URL` | No | Redis connection string | `redis://localhost:6379` |
| `SECRET_KEY` | Yes | Application secret key | - |
| `JWT_SECRET_KEY` | Yes | JWT signing key | - |
| `ENVIRONMENT` | No | Environment (development/production) | `development` |
| `API_HOST` | No | API host | `0.0.0.0` |
| `API_PORT` | No | API port | `8000` |

### Database Setup

#### PostgreSQL

```sql
-- Create database
CREATE DATABASE rynne;

-- Create user
CREATE USER rynne_user WITH PASSWORD 'secure_password';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE rynne TO rynne_user;
```

#### Redis

```bash
# Install Redis
sudo apt-get install redis-server

# Start Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

## 🔒 Security

### SSL/TLS Configuration

1. **Obtain SSL Certificate**
```bash
# Using Let's Encrypt
sudo certbot --nginx -d your-domain.com
```

2. **Configure Nginx**
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Firewall Configuration

```bash
# Allow HTTP and HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Allow SSH (if needed)
sudo ufw allow 22

# Enable firewall
sudo ufw enable
```

## 📊 Monitoring

### Health Checks

- **API Health**: `GET /health`
- **Database**: Connection pooling status
- **Redis**: Cache connectivity
- **External APIs**: Integration status

### Logging

```bash
# View application logs
docker-compose logs -f backend
docker-compose logs -f frontend

# View system logs
sudo journalctl -u docker
```

### Metrics

- Task completion rates
- API response times
- Database query performance
- Memory and CPU usage

## 🔄 Backup and Recovery

### Database Backup

```bash
# Create backup
pg_dump -h localhost -U rynne_user rynne > backup.sql

# Restore backup
psql -h localhost -U rynne_user rynne < backup.sql
```

### Application Data

```bash
# Backup configuration
tar -czf rynne-config-backup.tar.gz .env docker-compose.yml

# Backup logs
tar -czf rynne-logs-backup.tar.gz logs/
```

## 🚨 Troubleshooting

### Common Issues

1. **OpenAI API Errors**
   - Verify API key is correct
   - Check API quota and billing
   - Ensure network connectivity

2. **Database Connection Issues**
   - Verify PostgreSQL is running
   - Check connection string format
   - Ensure database exists

3. **Frontend Not Loading**
   - Check if backend is running
   - Verify API URL configuration
   - Check browser console for errors

### Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# Run with debug
docker-compose -f docker-compose.yml -f docker-compose.debug.yml up
```

## 📞 Support

For deployment issues:
1. Check logs: `docker-compose logs`
2. Verify configuration: `.env` file
3. Test connectivity: `curl http://localhost:8000/health`
4. Review documentation: `/docs`
