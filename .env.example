# Environment Configuration
ENVIRONMENT=development

# Database Configuration
DATABASE_URL=postgresql://rynne_user:rynne_password@localhost:5432/rynne
POSTGRES_DB=rynne
POSTGRES_USER=rynne_user
POSTGRES_PASSWORD=rynne_password

# Redis Configuration
REDIS_URL=redis://localhost:6379

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo-preview

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_ENVIRONMENT=development

# Security
SECRET_KEY=your_secret_key_here_change_in_production
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# External APIs (Optional)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_for_market_data
NEWS_API_KEY=your_news_api_key_for_market_intelligence
SLACK_BOT_TOKEN=your_slack_bot_token_for_task_integration
JIRA_API_TOKEN=your_jira_token_for_task_management

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Cache Configuration
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE=1000

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
FROM_EMAIL=<EMAIL>
