version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: rynne-postgres
    environment:
      POSTGRES_DB: rynne
      POSTGRES_USER: rynne_user
      POSTGRES_PASSWORD: rynne_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - rynne-network

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: rynne-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - rynne-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: rynne-backend
    environment:
      - DATABASE_URL=****************************************************/rynne
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENVIRONMENT=development
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/__pycache__
    depends_on:
      - postgres
      - redis
    networks:
      - rynne-network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: rynne-frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_ENVIRONMENT=development
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - rynne-network
    command: npm run dev

  # Nginx Reverse Proxy (for production)
  nginx:
    image: nginx:alpine
    container_name: rynne-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - rynne-network
    profiles:
      - production

volumes:
  postgres_data:
  redis_data:

networks:
  rynne-network:
    driver: bridge
