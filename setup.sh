#!/bin/bash

# Rynne AI CEO Agent Setup Script
# This script helps set up the development environment for <PERSON>ynne

set -e

echo "🧠 Setting up Rynne AI CEO Agent..."
echo "=================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    echo "Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please edit it with your configuration."
    echo ""
    echo "Required configurations:"
    echo "- OPENAI_API_KEY: Your OpenAI API key"
    echo "- SECRET_KEY: A secure secret key"
    echo "- JWT_SECRET_KEY: A secure JWT secret key"
    echo ""
    echo "Optional integrations:"
    echo "- JIRA_API_TOKEN: For Jira task integration"
    echo "- SLACK_BOT_TOKEN: For Slack integration"
    echo "- ALPHA_VANTAGE_API_KEY: For market data"
    echo "- NEWS_API_KEY: For news intelligence"
    echo ""
    read -p "Press Enter to continue after editing .env file..."
fi

# Generate secure keys if they're not set
if grep -q "your_secret_key_here" .env; then
    echo "🔐 Generating secure keys..."
    SECRET_KEY=$(openssl rand -hex 32)
    JWT_SECRET_KEY=$(openssl rand -hex 32)
    
    # Replace placeholder keys
    sed -i.bak "s/your_secret_key_here_change_in_production/$SECRET_KEY/" .env
    sed -i.bak "s/your_jwt_secret_key_here/$JWT_SECRET_KEY/" .env
    rm .env.bak
    echo "✅ Secure keys generated"
fi

# Check if OpenAI API key is set
if grep -q "your_openai_api_key_here" .env; then
    echo "⚠️  OpenAI API key not configured. Rynne's AI features will not work."
    echo "Please set OPENAI_API_KEY in your .env file."
fi

echo ""
echo "🚀 Starting Rynne services..."
echo "=============================="

# Build and start services
docker-compose up -d --build

echo ""
echo "⏳ Waiting for services to start..."
sleep 10

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    echo "✅ Services started successfully!"
    echo ""
    echo "🌐 Access Rynne:"
    echo "Frontend: http://localhost:3000"
    echo "API: http://localhost:8000"
    echo "API Docs: http://localhost:8000/docs"
    echo ""
    echo "📊 Database:"
    echo "PostgreSQL: localhost:5432"
    echo "Redis: localhost:6379"
    echo ""
    echo "🔧 Useful commands:"
    echo "View logs: docker-compose logs -f"
    echo "Stop services: docker-compose down"
    echo "Restart services: docker-compose restart"
    echo ""
    echo "🎉 Rynne AI CEO Agent is ready!"
    echo "Visit http://localhost:3000 to get started."
else
    echo "❌ Some services failed to start. Check logs with:"
    echo "docker-compose logs"
fi
