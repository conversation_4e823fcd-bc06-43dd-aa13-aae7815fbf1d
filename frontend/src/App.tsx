import { Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'

import Layout from '@/components/Layout'
import Dashboard from '@/pages/Dashboard'
import TaskManager from '@/pages/TaskManager'
import StrategicAnalysis from '@/pages/StrategicAnalysis'
import ScenarioSimulation from '@/pages/ScenarioSimulation'
import ChatInterface from '@/pages/ChatInterface'
import Settings from '@/pages/Settings'

function App() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Layout>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/tasks" element={<TaskManager />} />
            <Route path="/strategic" element={<StrategicAnalysis />} />
            <Route path="/scenarios" element={<ScenarioSimulation />} />
            <Route path="/chat" element={<ChatInterface />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </motion.div>
      </Layout>
    </div>
  )
}

export default App
