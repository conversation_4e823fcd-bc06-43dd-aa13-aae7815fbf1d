import { motion } from 'framer-motion'
import { 
  AlertTriangleIcon, 
  CalendarIcon, 
  UserIcon, 
  ArrowRightIcon,
  ClockIcon 
} from 'lucide-react'

interface Task {
  id: number
  title: string
  priority_category: string
  urgency: string
  importance: string
  assignee?: string
  deadline?: string
  is_overdue: boolean
}

interface TaskMatrixProps {
  tasks: Task[]
}

const priorityConfig = {
  DO_FIRST: {
    title: 'Do First',
    subtitle: 'Urgent & Important',
    color: 'red',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    textColor: 'text-red-800',
    icon: AlertTriangleIcon
  },
  SCHEDULE: {
    title: 'Schedule',
    subtitle: 'Not Urgent & Important',
    color: 'blue',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    textColor: 'text-blue-800',
    icon: CalendarIcon
  },
  DELEGATE: {
    title: 'Delegate',
    subtitle: 'Urgent & Not Important',
    color: 'yellow',
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    textColor: 'text-yellow-800',
    icon: UserIcon
  },
  DONT_DO: {
    title: "Don't Do",
    subtitle: 'Not Urgent & Not Important',
    color: 'gray',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    textColor: 'text-gray-800',
    icon: ArrowRightIcon
  }
}

export default function TaskMatrix({ tasks }: TaskMatrixProps) {
  // Group tasks by priority category
  const groupedTasks = tasks.reduce((acc, task) => {
    const category = task.priority_category as keyof typeof priorityConfig
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(task)
    return acc
  }, {} as Record<string, Task[]>)

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {Object.entries(priorityConfig).map(([category, config]) => {
        const categoryTasks = groupedTasks[category] || []
        const Icon = config.icon
        
        return (
          <motion.div
            key={category}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className={`${config.bgColor} ${config.borderColor} border-2 rounded-lg p-4`}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Icon className={`h-5 w-5 ${config.textColor}`} />
                <div>
                  <h3 className={`font-semibold ${config.textColor}`}>
                    {config.title}
                  </h3>
                  <p className={`text-sm ${config.textColor} opacity-75`}>
                    {config.subtitle}
                  </p>
                </div>
              </div>
              <span className={`badge badge-${config.color}`}>
                {categoryTasks.length}
              </span>
            </div>
            
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {categoryTasks.length === 0 ? (
                <p className={`text-sm ${config.textColor} opacity-50 text-center py-4`}>
                  No tasks in this category
                </p>
              ) : (
                categoryTasks.map((task) => (
                  <TaskCard key={task.id} task={task} />
                ))
              )}
            </div>
          </motion.div>
        )
      })}
    </div>
  )
}

function TaskCard({ task }: { task: Task }) {
  const isOverdue = task.is_overdue
  const hasDeadline = task.deadline
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white rounded-lg p-3 shadow-sm border ${
        isOverdue ? 'border-red-300 bg-red-50' : 'border-gray-200'
      }`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <h4 className={`text-sm font-medium truncate ${
            isOverdue ? 'text-red-900' : 'text-gray-900'
          }`}>
            {task.title}
          </h4>
          
          <div className="flex items-center space-x-3 mt-1">
            {task.assignee && (
              <div className="flex items-center space-x-1">
                <UserIcon className="h-3 w-3 text-gray-400" />
                <span className="text-xs text-gray-500 truncate">
                  {task.assignee}
                </span>
              </div>
            )}
            
            {hasDeadline && (
              <div className={`flex items-center space-x-1 ${
                isOverdue ? 'text-red-600' : 'text-gray-500'
              }`}>
                <ClockIcon className="h-3 w-3" />
                <span className="text-xs">
                  {new Date(task.deadline!).toLocaleDateString()}
                </span>
              </div>
            )}
          </div>
        </div>
        
        {isOverdue && (
          <AlertTriangleIcon className="h-4 w-4 text-red-500 flex-shrink-0 ml-2" />
        )}
      </div>
    </motion.div>
  )
}
