import { motion } from 'framer-motion'
import { LucideIcon } from 'lucide-react'

interface MetricsCardProps {
  title: string
  value: string | number
  change?: string
  changeType?: 'increase' | 'decrease' | 'neutral'
  icon: LucideIcon
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'secondary'
  description?: string
}

const colorClasses = {
  primary: {
    bg: 'bg-primary-100',
    text: 'text-primary-600',
    change: {
      increase: 'text-primary-600',
      decrease: 'text-primary-600',
      neutral: 'text-primary-600'
    }
  },
  success: {
    bg: 'bg-success-100',
    text: 'text-success-600',
    change: {
      increase: 'text-success-600',
      decrease: 'text-success-600',
      neutral: 'text-success-600'
    }
  },
  warning: {
    bg: 'bg-warning-100',
    text: 'text-warning-600',
    change: {
      increase: 'text-warning-600',
      decrease: 'text-warning-600',
      neutral: 'text-warning-600'
    }
  },
  danger: {
    bg: 'bg-danger-100',
    text: 'text-danger-600',
    change: {
      increase: 'text-danger-600',
      decrease: 'text-danger-600',
      neutral: 'text-danger-600'
    }
  },
  secondary: {
    bg: 'bg-secondary-100',
    text: 'text-secondary-600',
    change: {
      increase: 'text-secondary-600',
      decrease: 'text-secondary-600',
      neutral: 'text-secondary-600'
    }
  }
}

export default function MetricsCard({
  title,
  value,
  change,
  changeType = 'neutral',
  icon: Icon,
  color = 'primary',
  description
}: MetricsCardProps) {
  const colors = colorClasses[color]
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.02 }}
      className="card cursor-pointer transition-shadow hover:shadow-md"
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <div className={`p-2 rounded-lg ${colors.bg}`}>
              <Icon className={`h-5 w-5 ${colors.text}`} />
            </div>
          </div>
          
          <div className="flex items-baseline space-x-2">
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {change && (
              <span className={`text-sm font-medium ${colors.change[changeType]}`}>
                {change}
              </span>
            )}
          </div>
          
          {description && (
            <p className="text-xs text-gray-500 mt-1">{description}</p>
          )}
        </div>
      </div>
    </motion.div>
  )
}
