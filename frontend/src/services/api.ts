/**
 * API service for R<PERSON>e AI CEO Agent frontend.
 * 
 * This module provides typed API functions for interacting with the backend.
 */

import axios from 'axios'

const API_BASE_URL = import.meta.env.REACT_APP_API_URL || 'http://localhost:8000'

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('API Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// Types
export interface Task {
  id: number
  uuid: string
  title: string
  description?: string
  priority_category: string
  urgency: string
  importance: string
  priority_score: number
  status: string
  assignee?: string
  estimated_effort?: number
  actual_effort: number
  deadline?: string
  created_at: string
  updated_at: string
  business_impact?: string
  tags?: string[]
  is_overdue: boolean
  days_until_deadline?: number
  completion_percentage: number
}

export interface TaskStats {
  total_tasks: number
  by_status: Record<string, number>
  by_priority: Record<string, number>
  overdue_count: number
  completed_today: number
  total_estimated_effort: number
  total_actual_effort: number
}

export interface TaskMatrix {
  do_first: Task[]
  schedule: Task[]
  delegate: Task[]
  dont_do: Task[]
}

export interface DailySummary {
  date: string
  stats: TaskStats
  priority_matrix: TaskMatrix
  key_insights: string[]
  recommended_actions: string[]
  focus_areas: string[]
}

export interface ChatResponse {
  query: string
  response: {
    content: string
    type: string
    has_recommendations?: boolean
  }
  timestamp: string
  model: string
  context_used: boolean
}

// API Functions

// Task Management
export const taskApi = {
  // Get all tasks with optional filtering
  getTasks: async (params?: {
    skip?: number
    limit?: number
    priority?: string
    status?: string
    assignee?: string
  }): Promise<Task[]> => {
    const response = await api.get('/tasks', { params })
    return response.data
  },

  // Get single task
  getTask: async (taskId: number): Promise<Task> => {
    const response = await api.get(`/tasks/${taskId}`)
    return response.data
  },

  // Create new task
  createTask: async (taskData: {
    title: string
    description?: string
    urgency: string
    importance: string
    assignee?: string
    estimated_effort?: number
    deadline?: string
    business_impact?: string
    tags?: string[]
  }): Promise<Task> => {
    const response = await api.post('/tasks', taskData)
    return response.data
  },

  // Update task
  updateTask: async (taskId: number, updates: Partial<Task>): Promise<Task> => {
    const response = await api.put(`/tasks/${taskId}`, updates)
    return response.data
  },

  // Delete task
  deleteTask: async (taskId: number): Promise<void> => {
    await api.delete(`/tasks/${taskId}`)
  },

  // Get priority matrix
  getPriorityMatrix: async (): Promise<TaskMatrix> => {
    const response = await api.get('/tasks/matrix/priority')
    return response.data
  },

  // Get daily summary
  getDailySummary: async (): Promise<DailySummary> => {
    const response = await api.get('/tasks/summary/daily')
    return response.data
  },

  // Assign task
  assignTask: async (taskId: number, assignee: string, deadline?: string): Promise<Task> => {
    const response = await api.post(`/tasks/${taskId}/assign`, { assignee, deadline })
    return response.data.task
  },
}

// AI Chat
export const chatApi = {
  // Send query to AI
  sendQuery: async (query: string, context?: Record<string, any>): Promise<ChatResponse> => {
    const response = await api.post('/chat/query', { query, context })
    return response.data
  },

  // Get daily briefing
  getDailyBriefing: async (): Promise<any> => {
    const response = await api.get('/chat/briefing')
    return response.data
  },

  // Analyze task priorities
  analyzePriorities: async (): Promise<any> => {
    const response = await api.post('/chat/analyze-priorities')
    return response.data
  },

  // Generate executive report
  generateReport: async (reportType: string, timePeriod?: string): Promise<any> => {
    const response = await api.post('/chat/generate-report', {
      report_type: reportType,
      time_period: timePeriod,
      include_market_data: false
    })
    return response.data
  },
}

// Data Integration
export const integrationApi = {
  // Get integration status
  getStatus: async (): Promise<any> => {
    const response = await api.get('/integrations/status')
    return response.data
  },

  // Sync from Jira
  syncJira: async (): Promise<any> => {
    const response = await api.post('/integrations/sync/jira')
    return response.data
  },

  // Sync from Slack
  syncSlack: async (channelId?: string): Promise<any> => {
    const response = await api.post('/integrations/sync/slack', { channel_id: channelId })
    return response.data
  },

  // Sync all sources
  syncAll: async (): Promise<any> => {
    const response = await api.post('/integrations/sync/all')
    return response.data
  },

  // Get market intelligence
  getMarketIntelligence: async (): Promise<any> => {
    const response = await api.get('/integrations/market-intelligence')
    return response.data
  },

  // Test connector
  testConnector: async (connectorName: string): Promise<any> => {
    const response = await api.post(`/integrations/test/${connectorName}`)
    return response.data
  },
}

// Strategic Analysis
export const strategicApi = {
  // Get strategic overview
  getOverview: async (): Promise<any> => {
    const response = await api.get('/strategic')
    return response.data
  },

  // Get KPI analysis
  getKPIAnalysis: async (): Promise<any> => {
    const response = await api.get('/strategic/kpis')
    return response.data
  },

  // Get SWOT analysis
  getSWOTAnalysis: async (): Promise<any> => {
    const response = await api.get('/strategic/swot')
    return response.data
  },

  // Get market analysis
  getMarketAnalysis: async (): Promise<any> => {
    const response = await api.get('/strategic/market')
    return response.data
  },
}

// Scenario Simulation
export const scenarioApi = {
  // Get scenarios overview
  getOverview: async (): Promise<any> => {
    const response = await api.get('/scenarios')
    return response.data
  },

  // Simulate resource allocation
  simulateResourceAllocation: async (params: any): Promise<any> => {
    const response = await api.post('/scenarios/resource-allocation', params)
    return response.data
  },

  // Simulate market strategy
  simulateMarketStrategy: async (params: any): Promise<any> => {
    const response = await api.post('/scenarios/market-strategy', params)
    return response.data
  },

  // Simulate financial scenarios
  simulateFinancialScenarios: async (params: any): Promise<any> => {
    const response = await api.post('/scenarios/financial-planning', params)
    return response.data
  },
}

export default api
