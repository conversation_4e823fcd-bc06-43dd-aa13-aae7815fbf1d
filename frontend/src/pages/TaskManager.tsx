import { motion } from 'framer-motion'
import { CheckSquareIcon, PlusIcon } from 'lucide-react'

export default function TaskManager() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Task Management</h1>
          <p className="text-gray-600">Manage and prioritize tasks using the Eisenhower Matrix</p>
        </div>
        <button className="btn-primary">
          <PlusIcon className="h-4 w-4 mr-2" />
          New Task
        </button>
      </div>

      <div className="card">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <CheckSquareIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Task Management System</h3>
            <p className="text-gray-500">
              This module will implement intelligent task aggregation, Eisenhower Matrix prioritization,
              and automated assignment capabilities.
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
