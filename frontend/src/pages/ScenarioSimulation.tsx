import { motion } from 'framer-motion'
import { GitBranchIcon, PlayIcon } from 'lucide-react'

export default function ScenarioSimulation() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Scenario Simulation</h1>
          <p className="text-gray-600">What-if modeling for strategic decision making</p>
        </div>
        <button className="btn-primary">
          <PlayIcon className="h-4 w-4 mr-2" />
          Run Scenario
        </button>
      </div>

      <div className="card">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <GitBranchIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Scenario Simulation Module</h3>
            <p className="text-gray-500">
              This module will enable what-if scenario modeling for resource allocation,
              market strategy, and financial planning decisions.
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
