import { motion } from 'framer-motion'
import { SettingsIcon, SaveIcon } from 'lucide-react'

export default function Settings() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600">Configure Rynne AI CEO Agent preferences</p>
        </div>
        <button className="btn-primary">
          <SaveIcon className="h-4 w-4 mr-2" />
          Save Changes
        </button>
      </div>

      <div className="card">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <SettingsIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Configuration Settings</h3>
            <p className="text-gray-500">
              This module will provide configuration options for AI behavior,
              integration settings, and user preferences.
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
