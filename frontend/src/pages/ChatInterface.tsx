import { motion } from 'framer-motion'
import { MessageSquareIcon, SendIcon } from 'lucide-react'

export default function ChatInterface() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">AI Communication</h1>
          <p className="text-gray-600">Natural language interface for strategic queries</p>
        </div>
      </div>

      <div className="card h-96">
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <MessageSquareIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">AI Communication Interface</h3>
            <p className="text-gray-500">
              This module will provide natural language processing for executive-level
              strategic queries and actionable insights.
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
