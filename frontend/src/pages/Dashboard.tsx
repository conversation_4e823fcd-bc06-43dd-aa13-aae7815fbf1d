import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  CheckSquareIcon,
  TrendingUpIcon,
  GitBranchIcon,
  ClockIcon,
  AlertTriangleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  RefreshCwIcon,
} from 'lucide-react'
import toast from 'react-hot-toast'

import MetricsCard from '@/components/MetricsCard'
import TaskMatrix from '@/components/TaskMatrix'
import { taskApi, chatApi, type DailySummary, type Task } from '@/services/api'

export default function Dashboard() {
  const [dailySummary, setDailySummary] = useState<DailySummary | null>(null)
  const [tasks, setTasks] = useState<Task[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setIsLoading(true)

      // Load daily summary and tasks in parallel
      const [summaryResponse, tasksResponse] = await Promise.all([
        taskApi.getDailySummary().catch(() => null),
        taskApi.getTasks({ limit: 50 }).catch(() => [])
      ])

      setDailySummary(summaryResponse)
      setTasks(tasksResponse)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
      toast.error('Failed to load dashboard data')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRefresh = async () => {
    setIsRefreshing(true)
    await loadDashboardData()
    setIsRefreshing(false)
    toast.success('Dashboard refreshed')
  }

  // Generate stats from actual data
  const stats = dailySummary ? [
    {
      name: 'Active Tasks',
      value: dailySummary.stats.total_tasks.toString(),
      change: dailySummary.stats.completed_today > 0 ? `+${dailySummary.stats.completed_today} today` : undefined,
      changeType: 'increase' as const,
      icon: CheckSquareIcon,
      color: 'primary' as const,
    },
    {
      name: 'Critical Tasks',
      value: dailySummary.priority_matrix.do_first.length.toString(),
      change: dailySummary.stats.overdue_count > 0 ? `${dailySummary.stats.overdue_count} overdue` : 'On track',
      changeType: dailySummary.stats.overdue_count > 0 ? 'increase' as const : 'neutral' as const,
      icon: AlertTriangleIcon,
      color: dailySummary.stats.overdue_count > 0 ? 'danger' as const : 'success' as const,
    },
    {
      name: 'Completion Rate',
      value: `${Math.round((dailySummary.stats.by_status.completed || 0) / dailySummary.stats.total_tasks * 100)}%`,
      change: 'This week',
      changeType: 'neutral' as const,
      icon: TrendingUpIcon,
      color: 'success' as const,
    },
    {
      name: 'Effort Tracking',
      value: `${Math.round(dailySummary.stats.total_actual_effort)}h`,
      change: `${Math.round(dailySummary.stats.total_estimated_effort)}h estimated`,
      changeType: 'neutral' as const,
      icon: ClockIcon,
      color: 'warning' as const,
    },
  ] : []

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="h-24 bg-gray-200 rounded-lg"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold mb-2">Welcome back, CEO</h1>
            <p className="text-primary-100">
              {dailySummary
                ? `${dailySummary.stats.total_tasks} active tasks • ${dailySummary.priority_matrix.do_first.length} critical priorities`
                : 'Here\'s your strategic overview for today. Rynne has analyzed your priorities and prepared actionable insights.'
              }
            </p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="btn-secondary text-white border-white hover:bg-white hover:text-primary-600 disabled:opacity-50"
          >
            <RefreshCwIcon className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <MetricsCard
              title={stat.name}
              value={stat.value}
              change={stat.change}
              changeType={stat.changeType}
              icon={stat.icon}
              color={stat.color}
            />
          </motion.div>
        ))}
      </div>

      {/* Task Priority Matrix */}
      {dailySummary && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Task Priority Matrix</h2>
            <p className="text-gray-600">Tasks organized by the Eisenhower Matrix for strategic prioritization</p>
          </div>
          <TaskMatrix tasks={tasks} />
        </motion.div>
      )}

      {/* Strategic Insights */}
      {dailySummary && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="card"
        >
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">AI Strategic Insights</h3>
            <TrendingUpIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {dailySummary.key_insights.length > 0 ? (
              dailySummary.key_insights.map((insight, index) => (
                <div key={index} className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <p className="text-sm text-blue-800">{insight}</p>
                </div>
              ))
            ) : (
              <div className="p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">No strategic insights available. Try syncing your data sources.</p>
              </div>
            )}

            {dailySummary.recommended_actions.length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-semibold text-gray-900 mb-2">Recommended Actions</h4>
                <ul className="space-y-1">
                  {dailySummary.recommended_actions.slice(0, 3).map((action, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start">
                      <span className="text-primary-600 mr-2">•</span>
                      {action}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </motion.div>
      )}

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="card"
      >
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="btn-primary">
            <CheckSquareIcon className="h-4 w-4 mr-2" />
            Create Task
          </button>
          <button className="btn-secondary">
            <TrendingUpIcon className="h-4 w-4 mr-2" />
            Run Analysis
          </button>
          <button className="btn-secondary">
            <GitBranchIcon className="h-4 w-4 mr-2" />
            New Scenario
          </button>
          <button className="btn-secondary">
            <ClockIcon className="h-4 w-4 mr-2" />
            Schedule Review
          </button>
        </div>
      </motion.div>
    </div>
  )
}
