import { motion } from 'framer-motion'
import {
  CheckSquareIcon,
  TrendingUpIcon,
  GitBranchIcon,
  ClockIcon,
  AlertTriangleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from 'lucide-react'

export default function Dashboard() {
  const stats = [
    {
      name: 'Active Tasks',
      value: '24',
      change: '+12%',
      changeType: 'increase',
      icon: CheckSquareIcon,
      color: 'primary',
    },
    {
      name: 'Strategic Goals',
      value: '8',
      change: '+2',
      changeType: 'increase',
      icon: TrendingUpIcon,
      color: 'success',
    },
    {
      name: 'Scenarios Analyzed',
      value: '156',
      change: '+23%',
      changeType: 'increase',
      icon: GitBranchIcon,
      color: 'warning',
    },
    {
      name: 'Urgent Items',
      value: '3',
      change: '-2',
      changeType: 'decrease',
      icon: AlertTriangleIcon,
      color: 'danger',
    },
  ]

  const recentTasks = [
    {
      id: 1,
      title: 'Q4 Budget Review',
      priority: 'DO_FIRST',
      assignee: 'Finance Team',
      deadline: '2024-01-15',
      status: 'in_progress',
    },
    {
      id: 2,
      title: 'Market Expansion Analysis',
      priority: 'SCHEDULE',
      assignee: 'Strategy Team',
      deadline: '2024-01-20',
      status: 'pending',
    },
    {
      id: 3,
      title: 'Customer Satisfaction Survey',
      priority: 'DELEGATE',
      assignee: 'Marketing Team',
      deadline: '2024-01-18',
      status: 'completed',
    },
  ]

  const priorityColors = {
    DO_FIRST: 'bg-red-100 text-red-800',
    SCHEDULE: 'bg-blue-100 text-blue-800',
    DELEGATE: 'bg-yellow-100 text-yellow-800',
    DONT_DO: 'bg-gray-100 text-gray-800',
  }

  const statusColors = {
    pending: 'bg-gray-100 text-gray-800',
    in_progress: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white"
      >
        <h1 className="text-2xl font-bold mb-2">Welcome back, CEO</h1>
        <p className="text-primary-100">
          Here's your strategic overview for today. Rynne has analyzed your priorities and prepared actionable insights.
        </p>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="card"
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`p-3 rounded-lg bg-${stat.color}-100`}>
                  <stat.icon className={`h-6 w-6 text-${stat.color}-600`} />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">{stat.name}</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">{stat.value}</div>
                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.changeType === 'increase' ? (
                        <ArrowUpIcon className="self-center flex-shrink-0 h-4 w-4" />
                      ) : (
                        <ArrowDownIcon className="self-center flex-shrink-0 h-4 w-4" />
                      )}
                      <span className="ml-1">{stat.change}</span>
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Priority Tasks */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="card"
        >
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Priority Tasks</h3>
            <ClockIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {recentTasks.map((task) => (
              <div key={task.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-gray-900">{task.title}</h4>
                  <div className="flex items-center mt-1 space-x-2">
                    <span className={`badge ${priorityColors[task.priority as keyof typeof priorityColors]}`}>
                      {task.priority.replace('_', ' ')}
                    </span>
                    <span className="text-xs text-gray-500">{task.assignee}</span>
                  </div>
                </div>
                <div className="flex flex-col items-end">
                  <span className={`badge ${statusColors[task.status as keyof typeof statusColors]}`}>
                    {task.status.replace('_', ' ')}
                  </span>
                  <span className="text-xs text-gray-500 mt-1">{task.deadline}</span>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Strategic Insights */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
          className="card"
        >
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Strategic Insights</h3>
            <TrendingUpIcon className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h4 className="text-sm font-semibold text-blue-900 mb-2">Market Opportunity</h4>
              <p className="text-sm text-blue-800">
                Analysis shows 23% growth potential in the Southeast market. Recommend accelerating expansion timeline.
              </p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <h4 className="text-sm font-semibold text-green-900 mb-2">Operational Efficiency</h4>
              <p className="text-sm text-green-800">
                Process automation initiatives have improved productivity by 18%. Consider scaling to additional departments.
              </p>
            </div>
            <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <h4 className="text-sm font-semibold text-yellow-900 mb-2">Resource Allocation</h4>
              <p className="text-sm text-yellow-800">
                Engineering team at 95% capacity. Recommend hiring 2-3 senior developers to maintain velocity.
              </p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="card"
      >
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="btn-primary">
            <CheckSquareIcon className="h-4 w-4 mr-2" />
            Create Task
          </button>
          <button className="btn-secondary">
            <TrendingUpIcon className="h-4 w-4 mr-2" />
            Run Analysis
          </button>
          <button className="btn-secondary">
            <GitBranchIcon className="h-4 w-4 mr-2" />
            New Scenario
          </button>
          <button className="btn-secondary">
            <ClockIcon className="h-4 w-4 mr-2" />
            Schedule Review
          </button>
        </div>
      </motion.div>
    </div>
  )
}
