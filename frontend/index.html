<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/rynne-logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Rynne AI CEO Agent - Intelligent task management and strategic analysis for executive leadership" />
    <meta name="keywords" content="AI, CEO, task management, strategic analysis, business intelligence" />
    <meta name="author" content="Rynne AI" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://rynne.ai/" />
    <meta property="og:title" content="Rynne AI CEO Agent" />
    <meta property="og:description" content="Intelligent task management and strategic analysis for executive leadership" />
    <meta property="og:image" content="/rynne-og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://rynne.ai/" />
    <meta property="twitter:title" content="Rynne AI CEO Agent" />
    <meta property="twitter:description" content="Intelligent task management and strategic analysis for executive leadership" />
    <meta property="twitter:image" content="/rynne-og-image.png" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <title>Rynne AI CEO Agent</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
