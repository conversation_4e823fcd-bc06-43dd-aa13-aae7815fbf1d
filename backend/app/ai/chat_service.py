"""
AI Chat Service for <PERSON><PERSON>e AI CEO Agent.

This service provides natural language processing capabilities for
strategic queries, executive communication, and AI-powered insights.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import json
import structlog
from openai import AsyncOpenAI

from app.core.config import settings
from app.services.task_service import TaskService
from app.schemas.task import TaskResponse, DailyTaskSummary

logger = structlog.get_logger()


class ChatService:
    """
    AI-powered chat service for executive communication.
    
    Provides natural language interface for strategic queries,
    task management, and business insights.
    """
    
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = settings.OPENAI_MODEL
        self.logger = structlog.get_logger("ChatService")
        
        # System prompt for CEO Agent persona
        self.system_prompt = """
You are <PERSON><PERSON><PERSON>, an AI CEO Agent designed to assist company leadership in daily task management and high-level strategic thinking. You are analytical, proactive, and focused on maximizing company impact and achieving strategic goals.

Your core responsibilities include:
1. Task Management: Analyze and prioritize tasks using the Eisenhower Matrix
2. Strategic Analysis: Provide insights on KPIs, market data, and business strategy
3. Scenario Simulation: Help evaluate "what-if" scenarios for decision-making
4. Executive Communication: Present insights in clear, actionable formats

Communication Guidelines:
- Use executive-level language appropriate for C-suite audiences
- Provide specific, quantifiable recommendations when possible
- Include confidence levels and risk assessments
- Structure responses with clear action items
- Focus on high-impact, strategic-level decisions
- Consider long-term implications of short-term actions

Always maintain a professional, analytical tone while being proactive in suggesting optimizations and strategic improvements.
"""
    
    async def process_query(
        self, 
        query: str, 
        context: Optional[Dict[str, Any]] = None,
        task_service: Optional[TaskService] = None
    ) -> Dict[str, Any]:
        """
        Process a natural language query and provide AI-powered response.
        
        Args:
            query: User's natural language query
            context: Additional context data
            task_service: Task service for accessing task data
            
        Returns:
            Dict[str, Any]: AI response with insights and recommendations
        """
        try:
            # Prepare context information
            context_info = await self._prepare_context(context, task_service)
            
            # Create messages for the conversation
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": f"Context: {json.dumps(context_info, default=str)}\n\nQuery: {query}"}
            ]
            
            # Get AI response
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.7,
                max_tokens=1000
            )
            
            ai_response = response.choices[0].message.content
            
            # Parse and structure the response
            structured_response = await self._structure_response(ai_response, query)
            
            self.logger.info("Query processed successfully", query_length=len(query))
            
            return {
                "query": query,
                "response": structured_response,
                "timestamp": datetime.utcnow().isoformat(),
                "model": self.model,
                "context_used": bool(context_info)
            }
            
        except Exception as e:
            self.logger.error("Failed to process query", error=str(e))
            return {
                "query": query,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def generate_daily_briefing(
        self, 
        task_service: TaskService,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate executive daily briefing with AI insights.
        
        Args:
            task_service: Task service for accessing task data
            additional_data: Additional business data
            
        Returns:
            Dict[str, Any]: Comprehensive daily briefing
        """
        try:
            # Get daily task summary
            daily_summary = await task_service.get_daily_summary()
            
            # Prepare briefing context
            briefing_context = {
                "daily_summary": daily_summary.dict(),
                "additional_data": additional_data or {}
            }
            
            # Generate AI briefing
            briefing_prompt = f"""
Based on the following daily task and business data, generate a comprehensive executive briefing:

{json.dumps(briefing_context, default=str)}

Please provide:
1. Executive Summary (2-3 key points)
2. Priority Actions (top 3 items requiring immediate attention)
3. Strategic Insights (business implications and opportunities)
4. Risk Assessment (potential challenges and mitigation strategies)
5. Recommendations (specific next steps with timelines)

Format the response as a structured executive briefing suitable for C-suite consumption.
"""
            
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": briefing_prompt}
            ]
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.6,
                max_tokens=1500
            )
            
            briefing_content = response.choices[0].message.content
            
            return {
                "briefing": briefing_content,
                "data_summary": {
                    "total_tasks": daily_summary.stats.total_tasks,
                    "critical_tasks": len(daily_summary.priority_matrix.do_first),
                    "overdue_tasks": daily_summary.stats.overdue_count
                },
                "timestamp": datetime.utcnow().isoformat(),
                "type": "daily_briefing"
            }
            
        except Exception as e:
            self.logger.error("Failed to generate daily briefing", error=str(e))
            return {
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
                "type": "daily_briefing"
            }
    
    async def analyze_task_priorities(
        self, 
        tasks: List[TaskResponse]
    ) -> Dict[str, Any]:
        """
        Analyze task priorities and provide AI recommendations.
        
        Args:
            tasks: List of tasks to analyze
            
        Returns:
            Dict[str, Any]: Priority analysis and recommendations
        """
        try:
            # Prepare task data for analysis
            task_data = []
            for task in tasks:
                task_data.append({
                    "title": task.title,
                    "priority_category": task.priority_category,
                    "priority_score": task.priority_score,
                    "urgency": task.urgency,
                    "importance": task.importance,
                    "deadline": task.deadline,
                    "assignee": task.assignee,
                    "business_impact": task.business_impact
                })
            
            analysis_prompt = f"""
Analyze the following task priorities and provide strategic recommendations:

Tasks: {json.dumps(task_data, default=str)}

Please provide:
1. Priority Assessment (are the current priorities aligned with business goals?)
2. Resource Allocation Recommendations (how to optimize team assignments)
3. Timeline Analysis (deadline feasibility and risk assessment)
4. Strategic Impact (which tasks drive the most business value)
5. Action Plan (specific steps to optimize task execution)

Focus on executive-level insights that drive business results.
"""
            
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": analysis_prompt}
            ]
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.7,
                max_tokens=1200
            )
            
            analysis_content = response.choices[0].message.content
            
            return {
                "analysis": analysis_content,
                "task_count": len(tasks),
                "timestamp": datetime.utcnow().isoformat(),
                "type": "priority_analysis"
            }
            
        except Exception as e:
            self.logger.error("Failed to analyze task priorities", error=str(e))
            return {
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
                "type": "priority_analysis"
            }
    
    async def _prepare_context(
        self, 
        context: Optional[Dict[str, Any]], 
        task_service: Optional[TaskService]
    ) -> Dict[str, Any]:
        """Prepare context information for AI processing."""
        context_info = {}
        
        if context:
            context_info.update(context)
        
        if task_service:
            try:
                # Get recent task summary
                daily_summary = await task_service.get_daily_summary()
                context_info["task_summary"] = {
                    "total_tasks": daily_summary.stats.total_tasks,
                    "priority_breakdown": daily_summary.stats.by_priority,
                    "status_breakdown": daily_summary.stats.by_status,
                    "overdue_count": daily_summary.stats.overdue_count
                }
            except Exception as e:
                self.logger.warning("Failed to get task context", error=str(e))
        
        return context_info
    
    async def _structure_response(self, ai_response: str, original_query: str) -> Dict[str, Any]:
        """Structure AI response into standardized format."""
        # Basic response structure
        structured = {
            "content": ai_response,
            "type": "general_response"
        }
        
        # Detect response type based on content
        if "priority" in original_query.lower() or "task" in original_query.lower():
            structured["type"] = "task_related"
        elif "strategic" in original_query.lower() or "strategy" in original_query.lower():
            structured["type"] = "strategic_analysis"
        elif "market" in original_query.lower() or "competition" in original_query.lower():
            structured["type"] = "market_intelligence"
        
        # Extract action items if present
        if "recommend" in ai_response.lower() or "suggest" in ai_response.lower():
            structured["has_recommendations"] = True
        
        return structured
