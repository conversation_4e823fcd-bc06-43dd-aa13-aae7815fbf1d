"""
Task model for <PERSON><PERSON>e AI CEO Agent.

This module defines the Task model with Eisenhower Matrix categorization,
priority scoring, and intelligent assignment capabilities.
"""

from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import Column, Integer, String, Text, DateTime, Float, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy.dialects.postgresql import UUI<PERSON>, JSON<PERSON>
from sqlalchemy.orm import relationship
import uuid

from app.core.database import Base


class TaskPriority(str, Enum):
    """Eisenhower Matrix priority categories."""
    DO_FIRST = "DO_FIRST"        # Urgent & Important
    SCHEDULE = "SCHEDULE"        # Not Urgent & Important
    DELEGATE = "DELEGATE"        # Urgent & Not Important
    DONT_DO = "DONT_DO"         # Not Urgent & Not Important


class TaskStatus(str, Enum):
    """Task status enumeration."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ON_HOLD = "on_hold"


class TaskUrgency(str, Enum):
    """Task urgency levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class TaskImportance(str, Enum):
    """Task importance levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class Task(Base):
    """
    Task model with Eisenhower Matrix prioritization.
    
    Attributes:
        id: Unique task identifier
        title: Task title/summary
        description: Detailed task description
        priority_category: Eisenhower Matrix category
        urgency: Urgency level (1-4 scale)
        importance: Importance level (1-4 scale)
        priority_score: Calculated priority score
        status: Current task status
        assignee: Person or team assigned to task
        assignee_type: Individual or team assignment
        estimated_effort: Estimated hours to complete
        actual_effort: Actual hours spent
        deadline: Task deadline
        created_at: Creation timestamp
        updated_at: Last update timestamp
        completed_at: Completion timestamp
        business_impact: Expected business impact description
        dependencies: Task dependencies and relationships
        tags: Categorization tags
        metadata: Additional task metadata
    """
    
    __tablename__ = "tasks"
    
    # Primary fields
    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True)
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    
    # Eisenhower Matrix categorization
    priority_category = Column(String(20), nullable=False, index=True)
    urgency = Column(String(20), nullable=False)
    importance = Column(String(20), nullable=False)
    priority_score = Column(Float, nullable=False, index=True)
    
    # Status and assignment
    status = Column(String(20), nullable=False, default=TaskStatus.PENDING, index=True)
    assignee = Column(String(255), index=True)
    assignee_type = Column(String(20), default="individual")  # individual, team, department
    
    # Effort tracking
    estimated_effort = Column(Float)  # Hours
    actual_effort = Column(Float, default=0.0)  # Hours
    
    # Timing
    deadline = Column(DateTime, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime)
    
    # Business context
    business_impact = Column(Text)
    revenue_impact = Column(Float)  # Expected revenue impact
    cost_impact = Column(Float)     # Expected cost impact
    
    # Relationships and metadata
    dependencies = Column(JSONB)  # Task dependencies
    tags = Column(JSONB)         # Categorization tags
    metadata = Column(JSONB)     # Additional metadata
    
    # AI analysis fields
    ai_analysis = Column(JSONB)  # AI-generated insights
    confidence_score = Column(Float)  # AI confidence in prioritization
    
    def __repr__(self):
        return f"<Task(id={self.id}, title='{self.title}', priority='{self.priority_category}')>"
    
    @property
    def is_overdue(self) -> bool:
        """Check if task is overdue."""
        if not self.deadline:
            return False
        return datetime.utcnow() > self.deadline and self.status not in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]
    
    @property
    def days_until_deadline(self) -> Optional[int]:
        """Calculate days until deadline."""
        if not self.deadline:
            return None
        delta = self.deadline - datetime.utcnow()
        return delta.days
    
    @property
    def completion_percentage(self) -> float:
        """Calculate completion percentage based on effort."""
        if not self.estimated_effort or self.estimated_effort == 0:
            return 0.0
        return min(100.0, (self.actual_effort / self.estimated_effort) * 100)
    
    def calculate_priority_score(self) -> float:
        """
        Calculate priority score based on urgency, importance, and other factors.
        
        Returns:
            float: Priority score (0-100, higher = more priority)
        """
        # Base score from urgency and importance
        urgency_weights = {"low": 1, "medium": 2, "high": 3, "critical": 4}
        importance_weights = {"low": 1, "medium": 2, "high": 3, "critical": 4}
        
        urgency_score = urgency_weights.get(self.urgency, 1)
        importance_score = importance_weights.get(self.importance, 1)
        
        # Base priority score (0-16)
        base_score = urgency_score * importance_score
        
        # Apply deadline pressure
        deadline_multiplier = 1.0
        if self.deadline:
            days_left = self.days_until_deadline
            if days_left is not None:
                if days_left <= 0:
                    deadline_multiplier = 2.0  # Overdue
                elif days_left <= 1:
                    deadline_multiplier = 1.8  # Due today/tomorrow
                elif days_left <= 3:
                    deadline_multiplier = 1.5  # Due this week
                elif days_left <= 7:
                    deadline_multiplier = 1.2  # Due next week
        
        # Apply business impact
        impact_multiplier = 1.0
        if self.revenue_impact and self.revenue_impact > 0:
            # Higher revenue impact increases priority
            impact_multiplier += min(0.5, self.revenue_impact / 100000)  # Cap at 0.5
        
        # Calculate final score (0-100)
        final_score = (base_score * deadline_multiplier * impact_multiplier) * 6.25
        return min(100.0, final_score)
    
    def determine_eisenhower_category(self) -> TaskPriority:
        """
        Determine Eisenhower Matrix category based on urgency and importance.
        
        Returns:
            TaskPriority: The appropriate category
        """
        urgency_high = self.urgency in ["high", "critical"]
        importance_high = self.importance in ["high", "critical"]
        
        if urgency_high and importance_high:
            return TaskPriority.DO_FIRST
        elif not urgency_high and importance_high:
            return TaskPriority.SCHEDULE
        elif urgency_high and not importance_high:
            return TaskPriority.DELEGATE
        else:
            return TaskPriority.DONT_DO
    
    def update_priority(self):
        """Update priority category and score based on current attributes."""
        self.priority_category = self.determine_eisenhower_category().value
        self.priority_score = self.calculate_priority_score()
        self.updated_at = datetime.utcnow()
