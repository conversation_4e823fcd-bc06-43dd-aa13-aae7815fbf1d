"""
Market data connectors for R<PERSON>e AI CEO Agent.

This module provides connectors for market intelligence sources
including financial data, news feeds, and industry reports.
"""

from typing import Dict, List, Any, Optional
import httpx
from datetime import datetime, timedelta
import structlog

from app.integrations.base import BaseConnector
from app.core.config import settings

logger = structlog.get_logger()


class AlphaVantageConnector(BaseConnector):
    """
    Connector for Alpha Vantage financial data API.
    
    Fetches stock prices, financial indicators, and market data
    for strategic analysis and competitive intelligence.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config.get('api_key', settings.ALPHA_VANTAGE_API_KEY)
        self.base_url = "https://www.alphavantage.co/query"
        self.client: Optional[httpx.AsyncClient] = None
    
    async def connect(self) -> bool:
        """Establish connection to Alpha Vantage."""
        try:
            self.client = httpx.AsyncClient(timeout=30.0)
            return True
        except Exception as e:
            self.logger.error("Failed to connect to Alpha Vantage", error=str(e))
            return False
    
    async def disconnect(self) -> bool:
        """Close Alpha Vantage connection."""
        if self.client:
            await self.client.aclose()
            self.client = None
        return True
    
    async def test_connection(self) -> bool:
        """Test Alpha Vantage connection."""
        if not self.client:
            return False
        
        try:
            response = await self.client.get(
                self.base_url,
                params={
                    'function': 'GLOBAL_QUOTE',
                    'symbol': 'AAPL',
                    'apikey': self.api_key
                }
            )
            return response.status_code == 200 and 'Global Quote' in response.text
        except Exception as e:
            self.logger.error("Alpha Vantage connection test failed", error=str(e))
            return False
    
    async def fetch_data(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Fetch financial data from Alpha Vantage.
        
        Args:
            query: API query parameters (function, symbol, etc.)
            
        Returns:
            List[Dict[str, Any]]: Financial data
        """
        if not self.client:
            raise RuntimeError("Not connected to Alpha Vantage")
        
        function = query.get('function', 'TIME_SERIES_DAILY')
        symbol = query.get('symbol', 'SPY')
        
        try:
            params = {
                'function': function,
                'symbol': symbol,
                'apikey': self.api_key
            }
            
            # Add additional parameters based on function
            if 'interval' in query:
                params['interval'] = query['interval']
            if 'outputsize' in query:
                params['outputsize'] = query['outputsize']
            
            response = await self.client.get(self.base_url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # Extract time series data
            time_series_keys = [k for k in data.keys() if 'Time Series' in k or 'Daily' in k or 'Weekly' in k]
            if time_series_keys:
                time_series = data[time_series_keys[0]]
                result = []
                for date, values in time_series.items():
                    record = {'date': date, **values}
                    result.append(record)
                return result
            
            # Return raw data if no time series found
            return [data]
            
        except Exception as e:
            self.logger.error("Failed to fetch Alpha Vantage data", error=str(e))
            raise


class NewsAPIConnector(BaseConnector):
    """
    Connector for News API service.
    
    Fetches news articles and headlines for market intelligence
    and competitive analysis.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config.get('api_key', settings.NEWS_API_KEY)
        self.base_url = "https://newsapi.org/v2"
        self.client: Optional[httpx.AsyncClient] = None
    
    async def connect(self) -> bool:
        """Establish connection to News API."""
        try:
            self.client = httpx.AsyncClient(
                headers={"X-API-Key": self.api_key},
                timeout=30.0
            )
            return True
        except Exception as e:
            self.logger.error("Failed to connect to News API", error=str(e))
            return False
    
    async def disconnect(self) -> bool:
        """Close News API connection."""
        if self.client:
            await self.client.aclose()
            self.client = None
        return True
    
    async def test_connection(self) -> bool:
        """Test News API connection."""
        if not self.client:
            return False
        
        try:
            response = await self.client.get(
                f"{self.base_url}/top-headlines",
                params={'country': 'us', 'pageSize': 1}
            )
            return response.status_code == 200
        except Exception as e:
            self.logger.error("News API connection test failed", error=str(e))
            return False
    
    async def fetch_data(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Fetch news articles from News API.
        
        Args:
            query: News query parameters (q, sources, etc.)
            
        Returns:
            List[Dict[str, Any]]: News articles
        """
        if not self.client:
            raise RuntimeError("Not connected to News API")
        
        endpoint = query.get('endpoint', 'everything')
        params = {
            'q': query.get('q', 'business'),
            'language': query.get('language', 'en'),
            'sortBy': query.get('sortBy', 'publishedAt'),
            'pageSize': query.get('pageSize', 50)
        }
        
        # Add date range if specified
        if 'from_date' in query:
            params['from'] = query['from_date']
        if 'to_date' in query:
            params['to'] = query['to_date']
        
        try:
            response = await self.client.get(f"{self.base_url}/{endpoint}", params=params)
            response.raise_for_status()
            
            data = response.json()
            articles = data.get('articles', [])
            
            self.logger.info("Fetched news articles", count=len(articles))
            return articles
            
        except Exception as e:
            self.logger.error("Failed to fetch news data", error=str(e))
            raise


class EconomicDataConnector(BaseConnector):
    """
    Connector for economic data sources (FRED, World Bank, etc.).
    
    Fetches economic indicators and macroeconomic data
    for strategic planning and market analysis.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.source = config.get('source', 'fred')  # Federal Reserve Economic Data
        self.api_key = config.get('api_key')
        self.client: Optional[httpx.AsyncClient] = None
    
    async def connect(self) -> bool:
        """Establish connection to economic data source."""
        try:
            if self.source == 'fred':
                self.base_url = "https://api.stlouisfed.org/fred"
            else:
                self.base_url = config.get('base_url')
            
            self.client = httpx.AsyncClient(timeout=30.0)
            return True
        except Exception as e:
            self.logger.error("Failed to connect to economic data source", error=str(e))
            return False
    
    async def disconnect(self) -> bool:
        """Close economic data connection."""
        if self.client:
            await self.client.aclose()
            self.client = None
        return True
    
    async def test_connection(self) -> bool:
        """Test economic data connection."""
        if not self.client or self.source != 'fred':
            return True  # Placeholder for other sources
        
        try:
            response = await self.client.get(
                f"{self.base_url}/series",
                params={
                    'series_id': 'GDP',
                    'api_key': self.api_key,
                    'file_type': 'json',
                    'limit': 1
                }
            )
            return response.status_code == 200
        except Exception as e:
            self.logger.error("Economic data connection test failed", error=str(e))
            return False
    
    async def fetch_data(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Fetch economic data.
        
        Args:
            query: Economic data query parameters
            
        Returns:
            List[Dict[str, Any]]: Economic indicators
        """
        if not self.client:
            raise RuntimeError("Not connected to economic data source")
        
        if self.source == 'fred':
            return await self._fetch_fred_data(query)
        else:
            # Placeholder for other economic data sources
            return []
    
    async def _fetch_fred_data(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch data from FRED API."""
        series_id = query.get('series_id', 'GDP')
        limit = query.get('limit', 100)
        
        try:
            params = {
                'series_id': series_id,
                'api_key': self.api_key,
                'file_type': 'json',
                'limit': limit
            }
            
            if 'start_date' in query:
                params['observation_start'] = query['start_date']
            if 'end_date' in query:
                params['observation_end'] = query['end_date']
            
            response = await self.client.get(f"{self.base_url}/series/observations", params=params)
            response.raise_for_status()
            
            data = response.json()
            observations = data.get('observations', [])
            
            self.logger.info("Fetched FRED data", series=series_id, count=len(observations))
            return observations
            
        except Exception as e:
            self.logger.error("Failed to fetch FRED data", error=str(e))
            raise
