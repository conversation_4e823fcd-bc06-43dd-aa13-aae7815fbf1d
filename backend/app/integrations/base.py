"""
Base classes for data integrations in Rynne AI CEO Agent.

This module provides abstract base classes for implementing
data connectors and processors for various external systems.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from datetime import datetime
import structlog

logger = structlog.get_logger()


class BaseConnector(ABC):
    """
    Abstract base class for data connectors.
    
    All data connectors should inherit from this class and implement
    the required methods for connecting to external data sources.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = structlog.get_logger(self.__class__.__name__)
    
    @abstractmethod
    async def connect(self) -> bool:
        """
        Establish connection to the data source.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """
        Close connection to the data source.
        
        Returns:
            bool: True if disconnection successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """
        Test the connection to the data source.
        
        Returns:
            bool: True if connection is healthy, False otherwise
        """
        pass
    
    @abstractmethod
    async def fetch_data(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Fetch data from the source based on query parameters.
        
        Args:
            query: Query parameters specific to the data source
            
        Returns:
            List[Dict[str, Any]]: Retrieved data records
        """
        pass


class BaseProcessor(ABC):
    """
    Abstract base class for data processors.
    
    Data processors transform raw data from connectors into
    standardized formats for analysis and storage.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = structlog.get_logger(self.__class__.__name__)
    
    @abstractmethod
    async def process(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process raw data into standardized format.
        
        Args:
            raw_data: Raw data from connector
            
        Returns:
            List[Dict[str, Any]]: Processed data records
        """
        pass
    
    @abstractmethod
    def validate_data(self, data: Dict[str, Any]) -> bool:
        """
        Validate a single data record.
        
        Args:
            data: Data record to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        pass
    
    def clean_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Clean and normalize data record.
        
        Args:
            data: Raw data record
            
        Returns:
            Dict[str, Any]: Cleaned data record
        """
        # Default implementation - can be overridden
        cleaned = {}
        for key, value in data.items():
            # Remove None values and empty strings
            if value is not None and value != "":
                # Normalize string values
                if isinstance(value, str):
                    cleaned[key] = value.strip()
                else:
                    cleaned[key] = value
        return cleaned


class DataIntegrationManager:
    """
    Manager class for coordinating data integrations.
    
    This class orchestrates multiple connectors and processors
    to provide a unified data integration interface.
    """
    
    def __init__(self):
        self.connectors: Dict[str, BaseConnector] = {}
        self.processors: Dict[str, BaseProcessor] = {}
        self.logger = structlog.get_logger("DataIntegrationManager")
    
    def register_connector(self, name: str, connector: BaseConnector):
        """Register a data connector."""
        self.connectors[name] = connector
        self.logger.info("Connector registered", name=name, type=type(connector).__name__)
    
    def register_processor(self, name: str, processor: BaseProcessor):
        """Register a data processor."""
        self.processors[name] = processor
        self.logger.info("Processor registered", name=name, type=type(processor).__name__)
    
    async def fetch_and_process(
        self, 
        connector_name: str, 
        processor_name: str, 
        query: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Fetch data using a connector and process it.
        
        Args:
            connector_name: Name of registered connector
            processor_name: Name of registered processor
            query: Query parameters for data fetching
            
        Returns:
            List[Dict[str, Any]]: Processed data records
        """
        if connector_name not in self.connectors:
            raise ValueError(f"Connector '{connector_name}' not registered")
        
        if processor_name not in self.processors:
            raise ValueError(f"Processor '{processor_name}' not registered")
        
        connector = self.connectors[connector_name]
        processor = self.processors[processor_name]
        
        try:
            # Connect and fetch data
            await connector.connect()
            raw_data = await connector.fetch_data(query)
            
            # Process data
            processed_data = await processor.process(raw_data)
            
            self.logger.info(
                "Data integration completed",
                connector=connector_name,
                processor=processor_name,
                raw_records=len(raw_data),
                processed_records=len(processed_data)
            )
            
            return processed_data
            
        except Exception as e:
            self.logger.error(
                "Data integration failed",
                connector=connector_name,
                processor=processor_name,
                error=str(e)
            )
            raise
        finally:
            await connector.disconnect()
    
    async def health_check(self) -> Dict[str, bool]:
        """
        Check health of all registered connectors.
        
        Returns:
            Dict[str, bool]: Health status of each connector
        """
        health_status = {}
        
        for name, connector in self.connectors.items():
            try:
                await connector.connect()
                health_status[name] = await connector.test_connection()
                await connector.disconnect()
            except Exception as e:
                self.logger.error("Health check failed", connector=name, error=str(e))
                health_status[name] = False
        
        return health_status
