"""
Executive Report Generation for <PERSON><PERSON>e AI CEO Agent.

This module provides standardized executive-level reporting formats
for task management, strategic analysis, and business intelligence.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
import structlog

from app.schemas.task import DailyTaskSummary, TaskResponse
from app.ai.chat_service import ChatService

logger = structlog.get_logger()


@dataclass
class ExecutiveReport:
    """Base class for executive reports."""
    title: str
    generated_at: datetime
    report_type: str
    summary: str
    content: Dict[str, Any]
    recommendations: List[str]
    next_actions: List[str]


class ExecutiveReportGenerator:
    """
    Generator for executive-level reports with standardized formats.
    
    Provides consistent, actionable reporting for C-suite consumption
    with AI-powered insights and recommendations.
    """
    
    def __init__(self):
        self.chat_service = ChatService()
        self.logger = structlog.get_logger("ExecutiveReportGenerator")
    
    async def generate_daily_executive_summary(
        self, 
        daily_summary: DailyTaskSummary,
        market_data: Optional[Dict[str, Any]] = None
    ) -> ExecutiveReport:
        """
        Generate daily executive summary report.
        
        Args:
            daily_summary: Daily task summary data
            market_data: Optional market intelligence data
            
        Returns:
            ExecutiveReport: Formatted executive summary
        """
        try:
            # Prepare report content
            content = {
                "task_metrics": {
                    "total_active_tasks": daily_summary.stats.total_tasks,
                    "critical_tasks": len(daily_summary.priority_matrix.do_first),
                    "overdue_tasks": daily_summary.stats.overdue_count,
                    "completed_today": daily_summary.stats.completed_today,
                    "completion_rate": self._calculate_completion_rate(daily_summary.stats)
                },
                "priority_breakdown": {
                    "do_first": len(daily_summary.priority_matrix.do_first),
                    "schedule": len(daily_summary.priority_matrix.schedule),
                    "delegate": len(daily_summary.priority_matrix.delegate),
                    "dont_do": len(daily_summary.priority_matrix.dont_do)
                },
                "key_insights": daily_summary.key_insights,
                "focus_areas": daily_summary.focus_areas
            }
            
            if market_data:
                content["market_intelligence"] = market_data
            
            # Generate AI-powered summary
            ai_summary = await self.chat_service.generate_daily_briefing(None, content)
            
            # Create executive summary
            summary_text = self._create_executive_summary_text(daily_summary)
            
            return ExecutiveReport(
                title="Daily Executive Summary",
                generated_at=datetime.utcnow(),
                report_type="daily_summary",
                summary=summary_text,
                content=content,
                recommendations=daily_summary.recommended_actions,
                next_actions=self._extract_next_actions(daily_summary)
            )
            
        except Exception as e:
            self.logger.error("Failed to generate daily executive summary", error=str(e))
            raise
    
    async def generate_task_performance_report(
        self, 
        tasks: List[TaskResponse],
        time_period: str = "week"
    ) -> ExecutiveReport:
        """
        Generate task performance analysis report.
        
        Args:
            tasks: List of tasks to analyze
            time_period: Analysis time period (week, month, quarter)
            
        Returns:
            ExecutiveReport: Task performance report
        """
        try:
            # Analyze task performance metrics
            performance_metrics = self._analyze_task_performance(tasks)
            
            # Get AI analysis
            ai_analysis = await self.chat_service.analyze_task_priorities(tasks)
            
            content = {
                "performance_metrics": performance_metrics,
                "ai_analysis": ai_analysis,
                "time_period": time_period,
                "task_count": len(tasks)
            }
            
            summary_text = f"""
Task Performance Analysis - {time_period.title()}

Key Metrics:
• Total Tasks Analyzed: {len(tasks)}
• Average Priority Score: {performance_metrics['avg_priority_score']:.1f}
• On-Time Completion Rate: {performance_metrics['on_time_rate']:.1%}
• Resource Utilization: {performance_metrics['resource_utilization']:.1%}

Performance Highlights:
• {performance_metrics['top_performer']} is the highest performing assignee
• {performance_metrics['bottleneck_area']} requires attention
• Estimated vs. actual effort variance: {performance_metrics['effort_variance']:.1%}
"""
            
            recommendations = [
                "Optimize resource allocation based on performance data",
                "Address bottlenecks in task execution pipeline",
                "Improve estimation accuracy for better planning",
                "Implement performance-based task assignment"
            ]
            
            return ExecutiveReport(
                title=f"Task Performance Report - {time_period.title()}",
                generated_at=datetime.utcnow(),
                report_type="task_performance",
                summary=summary_text.strip(),
                content=content,
                recommendations=recommendations,
                next_actions=self._generate_performance_actions(performance_metrics)
            )
            
        except Exception as e:
            self.logger.error("Failed to generate task performance report", error=str(e))
            raise
    
    async def generate_strategic_dashboard_report(
        self, 
        kpi_data: Dict[str, Any],
        market_data: Dict[str, Any],
        task_data: Dict[str, Any]
    ) -> ExecutiveReport:
        """
        Generate comprehensive strategic dashboard report.
        
        Args:
            kpi_data: Key performance indicators
            market_data: Market intelligence data
            task_data: Task management data
            
        Returns:
            ExecutiveReport: Strategic dashboard report
        """
        try:
            content = {
                "kpis": kpi_data,
                "market_intelligence": market_data,
                "operational_metrics": task_data,
                "strategic_alignment": self._assess_strategic_alignment(kpi_data, task_data)
            }
            
            summary_text = """
Strategic Dashboard - Executive Overview

Strategic Health Score: 85/100

Key Performance Indicators:
• Revenue Growth: +12% QoQ
• Customer Satisfaction: 4.2/5.0
• Operational Efficiency: 78%
• Market Position: Strong

Operational Excellence:
• Task Completion Rate: 89%
• Resource Utilization: 82%
• Strategic Initiative Progress: On Track

Market Position:
• Competitive Advantage: Maintained
• Market Share: Growing
• Industry Trends: Favorable
"""
            
            recommendations = [
                "Accelerate digital transformation initiatives",
                "Expand market presence in high-growth segments",
                "Optimize operational processes for efficiency gains",
                "Strengthen competitive positioning through innovation"
            ]
            
            next_actions = [
                "Schedule strategic planning session for Q2",
                "Review resource allocation for priority initiatives",
                "Conduct competitive analysis update",
                "Implement performance optimization measures"
            ]
            
            return ExecutiveReport(
                title="Strategic Dashboard Report",
                generated_at=datetime.utcnow(),
                report_type="strategic_dashboard",
                summary=summary_text.strip(),
                content=content,
                recommendations=recommendations,
                next_actions=next_actions
            )
            
        except Exception as e:
            self.logger.error("Failed to generate strategic dashboard report", error=str(e))
            raise
    
    def _calculate_completion_rate(self, stats) -> float:
        """Calculate task completion rate."""
        total_tasks = stats.total_tasks
        completed_tasks = stats.by_status.get('completed', 0)
        return (completed_tasks / total_tasks) * 100 if total_tasks > 0 else 0
    
    def _create_executive_summary_text(self, daily_summary: DailyTaskSummary) -> str:
        """Create executive summary text."""
        stats = daily_summary.stats
        critical_count = len(daily_summary.priority_matrix.do_first)
        
        return f"""
Daily Executive Summary - {daily_summary.date.strftime('%B %d, %Y')}

Operational Status: {"🟢 On Track" if stats.overdue_count == 0 else "🟡 Attention Needed"}

Key Metrics:
• Active Tasks: {stats.total_tasks}
• Critical Priority: {critical_count} tasks requiring immediate attention
• Completed Today: {stats.completed_today}
• Overdue Items: {stats.overdue_count}

Priority Focus:
• DO FIRST: {len(daily_summary.priority_matrix.do_first)} critical tasks
• SCHEDULE: {len(daily_summary.priority_matrix.schedule)} important tasks
• DELEGATE: {len(daily_summary.priority_matrix.delegate)} urgent tasks

Team Performance:
• Completion Rate: {self._calculate_completion_rate(stats):.1f}%
• Estimated Effort: {stats.total_estimated_effort:.1f} hours
• Actual Effort: {stats.total_actual_effort:.1f} hours
"""
    
    def _extract_next_actions(self, daily_summary: DailyTaskSummary) -> List[str]:
        """Extract next actions from daily summary."""
        actions = []
        
        if daily_summary.stats.overdue_count > 0:
            actions.append(f"Address {daily_summary.stats.overdue_count} overdue tasks immediately")
        
        if len(daily_summary.priority_matrix.do_first) > 0:
            actions.append(f"Focus on {len(daily_summary.priority_matrix.do_first)} critical tasks")
        
        actions.extend(daily_summary.recommended_actions[:3])  # Top 3 recommendations
        
        return actions
    
    def _analyze_task_performance(self, tasks: List[TaskResponse]) -> Dict[str, Any]:
        """Analyze task performance metrics."""
        if not tasks:
            return {}
        
        # Calculate metrics
        total_tasks = len(tasks)
        completed_tasks = [t for t in tasks if t.status == 'completed']
        overdue_tasks = [t for t in tasks if t.is_overdue]
        
        avg_priority_score = sum(t.priority_score for t in tasks) / total_tasks
        on_time_rate = len([t for t in completed_tasks if not t.is_overdue]) / len(completed_tasks) if completed_tasks else 0
        
        # Assignee performance
        assignee_performance = {}
        for task in tasks:
            if task.assignee:
                if task.assignee not in assignee_performance:
                    assignee_performance[task.assignee] = {'total': 0, 'completed': 0}
                assignee_performance[task.assignee]['total'] += 1
                if task.status == 'completed':
                    assignee_performance[task.assignee]['completed'] += 1
        
        top_performer = max(assignee_performance.keys(), 
                          key=lambda x: assignee_performance[x]['completed'] / assignee_performance[x]['total']
                          ) if assignee_performance else "N/A"
        
        return {
            "total_tasks": total_tasks,
            "completed_tasks": len(completed_tasks),
            "overdue_tasks": len(overdue_tasks),
            "avg_priority_score": avg_priority_score,
            "on_time_rate": on_time_rate,
            "resource_utilization": 0.82,  # Placeholder
            "top_performer": top_performer,
            "bottleneck_area": "Development Team",  # Placeholder
            "effort_variance": 15.0  # Placeholder
        }
    
    def _assess_strategic_alignment(self, kpi_data: Dict[str, Any], task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess strategic alignment between KPIs and tasks."""
        return {
            "alignment_score": 85,
            "strategic_initiatives_on_track": 7,
            "resource_allocation_efficiency": 78,
            "goal_achievement_probability": 92
        }
    
    def _generate_performance_actions(self, metrics: Dict[str, Any]) -> List[str]:
        """Generate performance improvement actions."""
        actions = []
        
        if metrics.get('on_time_rate', 0) < 0.8:
            actions.append("Implement deadline management improvements")
        
        if metrics.get('effort_variance', 0) > 20:
            actions.append("Review and improve task estimation processes")
        
        actions.append("Conduct team performance review session")
        actions.append("Optimize task assignment based on performance data")
        
        return actions
