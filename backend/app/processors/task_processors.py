"""
Task data processors for <PERSON><PERSON>e AI CEO Agent.

This module provides processors to transform raw task data from various
sources into standardized formats for the Eisenhower Matrix system.
"""

from typing import Dict, List, Any
from datetime import datetime
import re
import structlog

from app.integrations.base import BaseProcessor
from app.models.task import TaskUrgency, TaskImportance

logger = structlog.get_logger()


class JiraTaskProcessor(BaseProcessor):
    """
    Processor for Jira issue data.
    
    Transforms Jira issues into standardized task format with
    automatic urgency and importance assessment.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.priority_mapping = {
            'Highest': TaskUrgency.CRITICAL,
            'High': TaskUrgency.HIGH,
            'Medium': TaskUrgency.MEDIUM,
            'Low': TaskUrgency.LOW,
            'Lowest': TaskUrgency.LOW
        }
    
    async def process(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process Jira issues into standardized task format.
        
        Args:
            raw_data: Raw Jira issues
            
        Returns:
            List[Dict[str, Any]]: Standardized tasks
        """
        processed_tasks = []
        
        for issue in raw_data:
            try:
                if self.validate_data(issue):
                    task = await self._transform_jira_issue(issue)
                    processed_tasks.append(task)
            except Exception as e:
                self.logger.error("Failed to process Jira issue", issue_key=issue.get('key'), error=str(e))
        
        self.logger.info("Processed Jira issues", input_count=len(raw_data), output_count=len(processed_tasks))
        return processed_tasks
    
    def validate_data(self, data: Dict[str, Any]) -> bool:
        """Validate Jira issue data."""
        required_fields = ['key', 'fields']
        return all(field in data for field in required_fields)
    
    async def _transform_jira_issue(self, issue: Dict[str, Any]) -> Dict[str, Any]:
        """Transform a single Jira issue."""
        fields = issue['fields']
        
        # Extract basic information
        title = fields.get('summary', 'Untitled Issue')
        description = fields.get('description', {})
        if isinstance(description, dict):
            description = description.get('content', [{}])[0].get('content', [{}])[0].get('text', '')
        
        # Determine urgency from priority
        priority = fields.get('priority', {}).get('name', 'Medium')
        urgency = self.priority_mapping.get(priority, TaskUrgency.MEDIUM)
        
        # Determine importance from issue type and labels
        issue_type = fields.get('issuetype', {}).get('name', '')
        labels = fields.get('labels', [])
        importance = self._assess_importance(issue_type, labels, description)
        
        # Extract assignee
        assignee_info = fields.get('assignee')
        assignee = assignee_info.get('displayName') if assignee_info else None
        
        # Extract deadline
        due_date = fields.get('duedate')
        deadline = datetime.fromisoformat(due_date) if due_date else None
        
        # Estimate effort from story points or time tracking
        story_points = fields.get('customfield_10016')  # Common story points field
        time_estimate = fields.get('timeoriginalestimate')
        estimated_effort = None
        
        if story_points:
            estimated_effort = float(story_points) * 4  # Assume 4 hours per story point
        elif time_estimate:
            estimated_effort = float(time_estimate) / 3600  # Convert seconds to hours
        
        # Assess business impact
        business_impact = self._assess_business_impact(issue_type, labels, description)
        
        return {
            'title': title,
            'description': description,
            'urgency': urgency.value,
            'importance': importance.value,
            'assignee': assignee,
            'estimated_effort': estimated_effort,
            'deadline': deadline,
            'business_impact': business_impact,
            'tags': labels + [issue_type],
            'source': 'jira',
            'source_id': issue['key'],
            'metadata': {
                'jira_key': issue['key'],
                'issue_type': issue_type,
                'status': fields.get('status', {}).get('name'),
                'project': fields.get('project', {}).get('key')
            }
        }
    
    def _assess_importance(self, issue_type: str, labels: List[str], description: str) -> TaskImportance:
        """Assess task importance based on issue characteristics."""
        # High importance indicators
        high_indicators = ['critical', 'blocker', 'security', 'production', 'customer']
        medium_indicators = ['enhancement', 'feature', 'improvement']
        
        # Check issue type
        if issue_type.lower() in ['bug', 'incident', 'outage']:
            return TaskImportance.HIGH
        
        # Check labels
        for label in labels:
            if any(indicator in label.lower() for indicator in high_indicators):
                return TaskImportance.HIGH
            if any(indicator in label.lower() for indicator in medium_indicators):
                return TaskImportance.MEDIUM
        
        # Check description for keywords
        description_lower = description.lower()
        if any(indicator in description_lower for indicator in high_indicators):
            return TaskImportance.HIGH
        
        return TaskImportance.MEDIUM
    
    def _assess_business_impact(self, issue_type: str, labels: List[str], description: str) -> str:
        """Assess business impact of the task."""
        if issue_type.lower() in ['bug', 'incident']:
            return "Potential customer impact and system stability"
        elif 'customer' in ' '.join(labels).lower():
            return "Direct customer experience impact"
        elif 'revenue' in description.lower():
            return "Revenue generation opportunity"
        else:
            return "Operational efficiency improvement"


class SlackTaskProcessor(BaseProcessor):
    """
    Processor for Slack messages that can be converted to tasks.
    
    Analyzes Slack messages for action items, deadlines, and priorities.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.action_patterns = [
            r'(?:todo|to do|action item|task):\s*(.+)',
            r'(?:need to|should|must)\s+(.+?)(?:\.|$)',
            r'(?:@\w+)\s+(?:please|can you)\s+(.+?)(?:\.|$)'
        ]
        self.urgency_keywords = {
            TaskUrgency.CRITICAL: ['urgent', 'asap', 'immediately', 'critical', 'emergency'],
            TaskUrgency.HIGH: ['important', 'priority', 'soon', 'today'],
            TaskUrgency.MEDIUM: ['this week', 'when possible'],
            TaskUrgency.LOW: ['eventually', 'someday', 'nice to have']
        }
    
    async def process(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process Slack messages into tasks.
        
        Args:
            raw_data: Raw Slack messages
            
        Returns:
            List[Dict[str, Any]]: Extracted tasks
        """
        processed_tasks = []
        
        for message in raw_data:
            try:
                if self.validate_data(message):
                    tasks = await self._extract_tasks_from_message(message)
                    processed_tasks.extend(tasks)
            except Exception as e:
                self.logger.error("Failed to process Slack message", error=str(e))
        
        self.logger.info("Processed Slack messages", input_count=len(raw_data), output_count=len(processed_tasks))
        return processed_tasks
    
    def validate_data(self, data: Dict[str, Any]) -> bool:
        """Validate Slack message data."""
        return 'text' in data and 'user' in data
    
    async def _extract_tasks_from_message(self, message: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract tasks from a Slack message."""
        text = message.get('text', '')
        user = message.get('user', 'unknown')
        timestamp = message.get('ts', '')
        
        tasks = []
        
        # Look for action items using patterns
        for pattern in self.action_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                task_text = match.group(1).strip()
                if len(task_text) > 5:  # Filter out very short matches
                    task = self._create_task_from_text(task_text, text, user, timestamp)
                    tasks.append(task)
        
        return tasks
    
    def _create_task_from_text(self, task_text: str, full_text: str, user: str, timestamp: str) -> Dict[str, Any]:
        """Create a task from extracted text."""
        # Assess urgency from keywords
        urgency = TaskUrgency.MEDIUM
        for level, keywords in self.urgency_keywords.items():
            if any(keyword in full_text.lower() for keyword in keywords):
                urgency = level
                break
        
        # Default importance based on context
        importance = TaskImportance.MEDIUM
        if '@channel' in full_text or '@here' in full_text:
            importance = TaskImportance.HIGH
        
        return {
            'title': task_text[:100],  # Truncate long titles
            'description': f"Extracted from Slack message: {full_text[:200]}...",
            'urgency': urgency.value,
            'importance': importance.value,
            'assignee': None,  # Will need to be assigned manually
            'estimated_effort': 1.0,  # Default 1 hour
            'deadline': None,
            'business_impact': "Team coordination and communication",
            'tags': ['slack', 'communication'],
            'source': 'slack',
            'source_id': timestamp,
            'metadata': {
                'slack_user': user,
                'slack_timestamp': timestamp,
                'original_message': full_text
            }
        }


class EmailTaskProcessor(BaseProcessor):
    """
    Processor for email messages that can be converted to tasks.
    
    Analyzes emails for action items, follow-ups, and deadlines.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.action_keywords = ['action required', 'please', 'need', 'follow up', 'deadline', 'due']
    
    async def process(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process emails into tasks.
        
        Args:
            raw_data: Raw email messages
            
        Returns:
            List[Dict[str, Any]]: Extracted tasks
        """
        # Placeholder implementation
        self.logger.info("Processing emails", count=len(raw_data))
        return []
    
    def validate_data(self, data: Dict[str, Any]) -> bool:
        """Validate email data."""
        return 'subject' in data and 'body' in data
