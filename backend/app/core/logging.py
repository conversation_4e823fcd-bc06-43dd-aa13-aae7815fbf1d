"""
Logging configuration for <PERSON><PERSON><PERSON> AI CEO Agent.

This module sets up structured logging using structlog with JSON formatting
for production and human-readable formatting for development.
"""

import logging
import sys
from typing import Any, Dict

import structlog
from structlog.stdlib import LoggerFactory

from app.core.config import settings


def setup_logging() -> None:
    """Configure structured logging for the application."""
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.LOG_LEVEL),
    )
    
    # Configure structlog
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]
    
    if settings.LOG_FORMAT == "json":
        # JSON formatting for production
        processors.append(structlog.processors.JSONRenderer())
    else:
        # Human-readable formatting for development
        processors.extend([
            structlog.dev.ConsoleRenderer(colors=True),
        ])
    
    structlog.configure(
        processors=processors,
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def get_logger(name: str = None) -> structlog.stdlib.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (optional)
        
    Returns:
        BoundLogger: Configured logger instance
    """
    return structlog.get_logger(name)


class LoggingMiddleware:
    """Middleware for request/response logging."""
    
    def __init__(self, app):
        self.app = app
        self.logger = get_logger("middleware")
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        request_id = scope.get("headers", {}).get("x-request-id", "unknown")
        
        # Log request
        self.logger.info(
            "Request started",
            method=scope["method"],
            path=scope["path"],
            request_id=request_id,
        )
        
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                self.logger.info(
                    "Request completed",
                    method=scope["method"],
                    path=scope["path"],
                    status_code=message["status"],
                    request_id=request_id,
                )
            await send(message)
        
        await self.app(scope, receive, send_wrapper)
