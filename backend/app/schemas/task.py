"""
Pydantic schemas for Task API endpoints.

This module defines request/response schemas for task management operations
with validation and serialization.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator

from app.models.task import TaskPriority, TaskStatus, TaskUrgency, TaskImportance


class TaskBase(BaseModel):
    """Base task schema with common fields."""
    title: str = Field(..., min_length=1, max_length=255, description="Task title")
    description: Optional[str] = Field(None, description="Detailed task description")
    urgency: TaskUrgency = Field(..., description="Task urgency level")
    importance: TaskImportance = Field(..., description="Task importance level")
    assignee: Optional[str] = Field(None, description="Assigned person or team")
    assignee_type: str = Field(default="individual", description="Assignment type")
    estimated_effort: Optional[float] = Field(None, ge=0, description="Estimated hours")
    deadline: Optional[datetime] = Field(None, description="Task deadline")
    business_impact: Optional[str] = Field(None, description="Expected business impact")
    revenue_impact: Optional[float] = Field(None, description="Expected revenue impact")
    cost_impact: Optional[float] = Field(None, description="Expected cost impact")
    tags: Optional[List[str]] = Field(default_factory=list, description="Task tags")
    dependencies: Optional[List[int]] = Field(default_factory=list, description="Task dependencies")


class TaskCreate(TaskBase):
    """Schema for creating a new task."""
    
    @validator('deadline')
    def deadline_must_be_future(cls, v):
        if v and v <= datetime.utcnow():
            raise ValueError('Deadline must be in the future')
        return v
    
    @validator('tags')
    def validate_tags(cls, v):
        if v and len(v) > 10:
            raise ValueError('Maximum 10 tags allowed')
        return v


class TaskUpdate(BaseModel):
    """Schema for updating an existing task."""
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    urgency: Optional[TaskUrgency] = None
    importance: Optional[TaskImportance] = None
    status: Optional[TaskStatus] = None
    assignee: Optional[str] = None
    assignee_type: Optional[str] = None
    estimated_effort: Optional[float] = Field(None, ge=0)
    actual_effort: Optional[float] = Field(None, ge=0)
    deadline: Optional[datetime] = None
    business_impact: Optional[str] = None
    revenue_impact: Optional[float] = None
    cost_impact: Optional[float] = None
    tags: Optional[List[str]] = None
    dependencies: Optional[List[int]] = None
    
    @validator('deadline')
    def deadline_validation(cls, v):
        if v and v <= datetime.utcnow():
            raise ValueError('Deadline must be in the future')
        return v


class TaskResponse(BaseModel):
    """Schema for task API responses."""
    id: int
    uuid: str
    title: str
    description: Optional[str]
    priority_category: TaskPriority
    urgency: TaskUrgency
    importance: TaskImportance
    priority_score: float
    status: TaskStatus
    assignee: Optional[str]
    assignee_type: str
    estimated_effort: Optional[float]
    actual_effort: float
    deadline: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime]
    business_impact: Optional[str]
    revenue_impact: Optional[float]
    cost_impact: Optional[float]
    tags: Optional[List[str]]
    dependencies: Optional[List[int]]
    is_overdue: bool
    days_until_deadline: Optional[int]
    completion_percentage: float
    confidence_score: Optional[float]
    
    class Config:
        from_attributes = True


class TaskPriorityMatrix(BaseModel):
    """Schema for Eisenhower Matrix task organization."""
    do_first: List[TaskResponse] = Field(description="Urgent & Important tasks")
    schedule: List[TaskResponse] = Field(description="Not Urgent & Important tasks")
    delegate: List[TaskResponse] = Field(description="Urgent & Not Important tasks")
    dont_do: List[TaskResponse] = Field(description="Not Urgent & Not Important tasks")
    
    class Config:
        schema_extra = {
            "example": {
                "do_first": [],
                "schedule": [],
                "delegate": [],
                "dont_do": []
            }
        }


class TaskSummaryStats(BaseModel):
    """Task summary statistics."""
    total_tasks: int
    by_status: Dict[str, int]
    by_priority: Dict[str, int]
    overdue_count: int
    completed_today: int
    avg_completion_time: Optional[float]
    total_estimated_effort: float
    total_actual_effort: float


class DailyTaskSummary(BaseModel):
    """Comprehensive daily task summary for executives."""
    date: datetime
    stats: TaskSummaryStats
    priority_matrix: TaskPriorityMatrix
    key_insights: List[str] = Field(description="AI-generated insights")
    recommended_actions: List[str] = Field(description="Recommended next steps")
    focus_areas: List[str] = Field(description="Areas requiring attention")
    
    class Config:
        schema_extra = {
            "example": {
                "date": "2024-01-15T00:00:00Z",
                "stats": {
                    "total_tasks": 24,
                    "by_status": {"pending": 8, "in_progress": 12, "completed": 4},
                    "by_priority": {"DO_FIRST": 3, "SCHEDULE": 8, "DELEGATE": 10, "DONT_DO": 3},
                    "overdue_count": 2,
                    "completed_today": 4,
                    "avg_completion_time": 2.5,
                    "total_estimated_effort": 120.0,
                    "total_actual_effort": 95.5
                },
                "key_insights": [
                    "3 critical tasks require immediate attention",
                    "Engineering team at 95% capacity",
                    "Marketing initiatives showing strong ROI"
                ],
                "recommended_actions": [
                    "Prioritize Q4 budget review completion",
                    "Delegate routine tasks to free up strategic time",
                    "Schedule market expansion planning session"
                ],
                "focus_areas": [
                    "Revenue-critical activities",
                    "Team capacity optimization",
                    "Strategic planning initiatives"
                ]
            }
        }


class TaskAssignmentRequest(BaseModel):
    """Schema for task assignment requests."""
    assignee: str = Field(..., description="Person or team to assign task to")
    deadline: Optional[datetime] = Field(None, description="Assignment deadline")
    notes: Optional[str] = Field(None, description="Assignment notes")
    
    @validator('deadline')
    def deadline_must_be_future(cls, v):
        if v and v <= datetime.utcnow():
            raise ValueError('Assignment deadline must be in the future')
        return v


class TaskBulkOperation(BaseModel):
    """Schema for bulk task operations."""
    task_ids: List[int] = Field(..., min_items=1, description="List of task IDs")
    operation: str = Field(..., description="Operation to perform")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Operation parameters")
    
    @validator('operation')
    def validate_operation(cls, v):
        allowed_operations = ['update_status', 'assign', 'update_priority', 'delete']
        if v not in allowed_operations:
            raise ValueError(f'Operation must be one of: {allowed_operations}')
        return v
