"""
Rynne AI CEO Agent - Main FastAPI Application

This is the entry point for the Rynne AI CEO Agent backend API.
It provides endpoints for task management, strategic analysis, and scenario simulation.
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import structlog
import uvicorn

from app.core.config import settings
from app.core.database import engine, Base
from app.api.v1.api import api_router
from app.core.logging import setup_logging

# Setup structured logging
setup_logging()
logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    logger.info("Starting Rynne AI CEO Agent", version="1.0.0")
    
    # Create database tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("Database tables created successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down <PERSON>ynne AI CEO Agent")


# Create FastAPI application
app = <PERSON><PERSON>I(
    title="<PERSON>ynne AI CEO Agent",
    description="""
    <PERSON><PERSON><PERSON> is an AI-powered CEO Agent designed to assist company leadership 
    in daily task management and high-level strategic thinking.
    
    ## Core Capabilities
    
    * **Task Management**: Smart aggregation, Eisenhower Matrix prioritization
    * **Strategic Analysis**: KPI monitoring, market intelligence, framework application
    * **Scenario Simulation**: What-if modeling, resource optimization
    * **Executive Communication**: Natural language interface, actionable insights
    """,
    version="1.0.0",
    docs_url="/docs" if settings.ENVIRONMENT == "development" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT == "development" else None,
    lifespan=lifespan,
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)

# Include API routes
app.include_router(api_router, prefix="/api/v1")


@app.get("/", tags=["Root"])
async def root():
    """Root endpoint providing basic API information."""
    return {
        "message": "Welcome to Rynne AI CEO Agent",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "operational"
    }


@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint for monitoring and load balancers."""
    try:
        # Add database connectivity check here if needed
        return {
            "status": "healthy",
            "service": "rynne-ai-ceo-agent",
            "version": "1.0.0",
            "environment": settings.ENVIRONMENT
        }
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for unhandled errors."""
    logger.error(
        "Unhandled exception",
        path=request.url.path,
        method=request.method,
        error=str(exc),
        exc_info=True
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "request_id": getattr(request.state, "request_id", None)
        }
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.ENVIRONMENT == "development",
        workers=settings.API_WORKERS if settings.ENVIRONMENT == "production" else 1,
        log_config=None,  # Use our custom logging
    )
