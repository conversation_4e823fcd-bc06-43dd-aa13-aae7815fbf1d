"""
Data Integration API endpoints for Rynne AI CEO Agent.

This module provides endpoints for managing data integrations,
syncing data from external sources, and monitoring integration health.
"""

from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from app.core.database import get_db
from app.services.integration_service import IntegrationService
from app.services.task_service import TaskService

logger = structlog.get_logger()
router = APIRouter()

# Global integration service instance
integration_service = IntegrationService()


@router.get("/status")
async def get_integration_status():
    """
    Get status of all data integrations.
    
    Returns health status of all configured connectors and processors.
    """
    try:
        status = await integration_service.get_integration_status()
        return status
    except Exception as e:
        logger.error("Failed to get integration status", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get integration status")


@router.post("/sync/jira")
async def sync_jira_tasks(
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    Sync tasks from Jira.
    
    Fetches issues from Jira and creates corresponding tasks
    in the Rynne system with automatic prioritization.
    """
    try:
        task_service = TaskService(db)
        
        # Run sync in background for better performance
        background_tasks.add_task(
            _background_jira_sync,
            task_service
        )
        
        return {
            "message": "Jira sync started",
            "status": "processing"
        }
        
    except Exception as e:
        logger.error("Failed to start Jira sync", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to start Jira sync")


@router.post("/sync/slack")
async def sync_slack_tasks(
    channel_id: Optional[str] = None,
    background_tasks: BackgroundTasks = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Sync tasks from Slack messages.
    
    Analyzes Slack messages for action items and creates
    corresponding tasks in the system.
    """
    try:
        task_service = TaskService(db)
        
        # Run sync in background
        background_tasks.add_task(
            _background_slack_sync,
            task_service,
            channel_id
        )
        
        return {
            "message": "Slack sync started",
            "status": "processing",
            "channel_id": channel_id
        }
        
    except Exception as e:
        logger.error("Failed to start Slack sync", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to start Slack sync")


@router.post("/sync/all")
async def sync_all_sources(
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    Sync data from all configured sources.
    
    Runs a comprehensive sync across all available data sources
    including task management systems and market intelligence.
    """
    try:
        task_service = TaskService(db)
        
        # Run comprehensive sync in background
        background_tasks.add_task(
            _background_full_sync,
            task_service
        )
        
        return {
            "message": "Full sync started",
            "status": "processing"
        }
        
    except Exception as e:
        logger.error("Failed to start full sync", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to start full sync")


@router.get("/market-intelligence")
async def get_market_intelligence():
    """
    Get latest market intelligence data.
    
    Returns aggregated market data, news, and economic indicators
    for strategic analysis.
    """
    try:
        intelligence_data = await integration_service.fetch_market_intelligence()
        return {
            "timestamp": intelligence_data.get('timestamp'),
            "data": intelligence_data,
            "status": "success"
        }
    except Exception as e:
        logger.error("Failed to fetch market intelligence", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to fetch market intelligence")


@router.get("/connectors")
async def list_connectors():
    """
    List all available data connectors.
    
    Returns information about configured connectors and their capabilities.
    """
    try:
        connectors = list(integration_service.integration_manager.connectors.keys())
        processors = list(integration_service.integration_manager.processors.keys())
        
        return {
            "connectors": connectors,
            "processors": processors,
            "total_connectors": len(connectors),
            "total_processors": len(processors)
        }
    except Exception as e:
        logger.error("Failed to list connectors", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to list connectors")


@router.post("/test/{connector_name}")
async def test_connector(connector_name: str):
    """
    Test a specific data connector.
    
    Tests the connection to a specific data source and returns
    the health status.
    """
    try:
        if connector_name not in integration_service.integration_manager.connectors:
            raise HTTPException(status_code=404, detail=f"Connector '{connector_name}' not found")
        
        connector = integration_service.integration_manager.connectors[connector_name]
        
        # Test connection
        await connector.connect()
        is_healthy = await connector.test_connection()
        await connector.disconnect()
        
        return {
            "connector": connector_name,
            "status": "healthy" if is_healthy else "unhealthy",
            "timestamp": "2024-01-15T10:00:00Z"
        }
        
    except Exception as e:
        logger.error("Connector test failed", connector=connector_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"Connector test failed: {str(e)}")


# Background task functions
async def _background_jira_sync(task_service: TaskService):
    """Background task for Jira sync."""
    try:
        result = await integration_service.sync_tasks_from_jira(task_service)
        logger.info("Background Jira sync completed", result=result)
    except Exception as e:
        logger.error("Background Jira sync failed", error=str(e))


async def _background_slack_sync(task_service: TaskService, channel_id: Optional[str]):
    """Background task for Slack sync."""
    try:
        result = await integration_service.sync_tasks_from_slack(task_service, channel_id)
        logger.info("Background Slack sync completed", result=result)
    except Exception as e:
        logger.error("Background Slack sync failed", error=str(e))


async def _background_full_sync(task_service: TaskService):
    """Background task for full sync."""
    try:
        result = await integration_service.run_scheduled_sync(task_service)
        logger.info("Background full sync completed", result=result)
    except Exception as e:
        logger.error("Background full sync failed", error=str(e))
