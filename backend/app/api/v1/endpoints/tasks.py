"""
Task Management API endpoints for <PERSON><PERSON>e AI CEO Agent.

This module provides endpoints for task aggregation, prioritization using
the Eisenhower Matrix, and task assignment with intelligent routing.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from app.core.database import get_db
from app.models.task import Task
from app.services.task_service import TaskService
from app.schemas.task import (
    TaskCreate,
    TaskUpdate,
    TaskResponse,
    TaskPriorityMatrix,
    DailyTaskSummary
)

logger = structlog.get_logger()
router = APIRouter()


@router.post("/", response_model=TaskResponse)
async def create_task(
    task: TaskCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new task with automatic prioritization.
    
    The task will be automatically categorized using the Eisenhower Matrix
    based on urgency and importance indicators.
    """
    try:
        task_service = TaskService(db)
        created_task = await task_service.create_task(task)
        
        logger.info(
            "Task created successfully",
            task_id=created_task.id,
            title=created_task.title,
            priority=created_task.priority_category
        )
        
        return created_task
    except Exception as e:
        logger.error("Failed to create task", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to create task")


@router.get("/", response_model=List[TaskResponse])
async def get_tasks(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    priority: Optional[str] = Query(None, description="Filter by priority category"),
    status: Optional[str] = Query(None, description="Filter by task status"),
    assignee: Optional[str] = Query(None, description="Filter by assignee"),
    db: AsyncSession = Depends(get_db)
):
    """
    Retrieve tasks with optional filtering and pagination.
    
    Supports filtering by priority category (DO_FIRST, SCHEDULE, DELEGATE, DONT_DO),
    status, and assignee.
    """
    try:
        task_service = TaskService(db)
        tasks = await task_service.get_tasks(
            skip=skip,
            limit=limit,
            priority=priority,
            status=status,
            assignee=assignee
        )
        
        logger.info(
            "Tasks retrieved successfully",
            count=len(tasks),
            filters={"priority": priority, "status": status, "assignee": assignee}
        )
        
        return tasks
    except Exception as e:
        logger.error("Failed to retrieve tasks", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve tasks")


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Get a specific task by ID."""
    try:
        task_service = TaskService(db)
        task = await task_service.get_task(task_id)
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return task
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to retrieve task", task_id=task_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve task")


@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: int,
    task_update: TaskUpdate,
    db: AsyncSession = Depends(get_db)
):
    """
    Update a task with automatic re-prioritization.
    
    When task details are updated, the system will automatically
    re-evaluate the priority using the Eisenhower Matrix.
    """
    try:
        task_service = TaskService(db)
        updated_task = await task_service.update_task(task_id, task_update)
        
        if not updated_task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        logger.info(
            "Task updated successfully",
            task_id=task_id,
            priority=updated_task.priority_category
        )
        
        return updated_task
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update task", task_id=task_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to update task")


@router.delete("/{task_id}")
async def delete_task(
    task_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Delete a task."""
    try:
        task_service = TaskService(db)
        success = await task_service.delete_task(task_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Task not found")
        
        logger.info("Task deleted successfully", task_id=task_id)
        return {"message": "Task deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete task", task_id=task_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to delete task")


@router.get("/matrix/priority", response_model=TaskPriorityMatrix)
async def get_priority_matrix(
    db: AsyncSession = Depends(get_db)
):
    """
    Get tasks organized by Eisenhower Matrix categories.
    
    Returns tasks categorized into:
    - DO_FIRST: Urgent & Important
    - SCHEDULE: Not Urgent & Important  
    - DELEGATE: Urgent & Not Important
    - DONT_DO: Not Urgent & Not Important
    """
    try:
        task_service = TaskService(db)
        matrix = await task_service.get_priority_matrix()
        
        logger.info("Priority matrix retrieved successfully")
        return matrix
    except Exception as e:
        logger.error("Failed to retrieve priority matrix", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve priority matrix")


@router.get("/summary/daily", response_model=DailyTaskSummary)
async def get_daily_summary(
    db: AsyncSession = Depends(get_db)
):
    """
    Generate comprehensive daily task summary.
    
    Provides executive-level overview of:
    - Task distribution by priority
    - Progress metrics
    - Key focus areas
    - Recommended actions
    """
    try:
        task_service = TaskService(db)
        summary = await task_service.get_daily_summary()
        
        logger.info("Daily summary generated successfully")
        return summary
    except Exception as e:
        logger.error("Failed to generate daily summary", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to generate daily summary")


@router.post("/{task_id}/assign")
async def assign_task(
    task_id: int,
    assignee: str,
    deadline: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Assign a task to a team member or team.
    
    Uses intelligent routing to suggest optimal assignees based on
    task requirements, team capacity, and expertise.
    """
    try:
        task_service = TaskService(db)
        assigned_task = await task_service.assign_task(task_id, assignee, deadline)
        
        if not assigned_task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        logger.info(
            "Task assigned successfully",
            task_id=task_id,
            assignee=assignee,
            deadline=deadline
        )
        
        return {"message": "Task assigned successfully", "task": assigned_task}
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to assign task", task_id=task_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to assign task")
