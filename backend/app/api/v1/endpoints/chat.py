"""
AI Communication API endpoints for <PERSON><PERSON>e AI CEO Agent.

This module provides endpoints for natural language interaction,
strategic queries, and AI-powered insights.
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
import structlog

from app.core.database import get_db
from app.ai.chat_service import ChatService
from app.services.task_service import TaskService
from app.reports.executive_reports import ExecutiveReportGenerator

logger = structlog.get_logger()
router = APIRouter()

# Initialize services
chat_service = ChatService()
report_generator = ExecutiveReportGenerator()


class ChatQuery(BaseModel):
    """Schema for chat queries."""
    query: str
    context: Optional[Dict[str, Any]] = None


class ReportRequest(BaseModel):
    """Schema for report generation requests."""
    report_type: str
    time_period: Optional[str] = "week"
    include_market_data: bool = False


@router.get("/")
async def get_chat_overview():
    """
    Get AI communication interface overview.

    Provides information about available AI capabilities
    and communication features.
    """
    return {
        "message": "<PERSON><PERSON>e AI CEO Agent - Communication Interface",
        "description": "Natural language processing for strategic queries and executive insights",
        "capabilities": [
            "Strategic query processing",
            "Task priority analysis",
            "Executive briefing generation",
            "Business intelligence insights",
            "Scenario analysis support"
        ],
        "status": "operational"
    }


@router.post("/query")
async def process_strategic_query(
    chat_query: ChatQuery,
    db: AsyncSession = Depends(get_db)
):
    """
    Process natural language strategic queries.

    Accepts natural language questions about business strategy,
    performance, and provides AI-powered insights and recommendations.
    """
    try:
        task_service = TaskService(db)

        # Process the query with AI
        response = await chat_service.process_query(
            query=chat_query.query,
            context=chat_query.context,
            task_service=task_service
        )

        logger.info("Strategic query processed", query_length=len(chat_query.query))
        return response

    except Exception as e:
        logger.error("Failed to process strategic query", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to process query")


@router.get("/briefing")
async def get_daily_briefing(
    db: AsyncSession = Depends(get_db)
):
    """
    Get AI-generated daily executive briefing.

    Provides comprehensive daily briefing with task analysis,
    strategic insights, and actionable recommendations.
    """
    try:
        task_service = TaskService(db)

        # Generate daily briefing
        briefing = await chat_service.generate_daily_briefing(task_service)

        logger.info("Daily briefing generated successfully")
        return briefing

    except Exception as e:
        logger.error("Failed to generate daily briefing", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to generate briefing")


@router.post("/analyze-priorities")
async def analyze_task_priorities(
    db: AsyncSession = Depends(get_db)
):
    """
    Analyze current task priorities with AI insights.

    Provides AI-powered analysis of task priorities and
    recommendations for optimization.
    """
    try:
        task_service = TaskService(db)

        # Get current tasks
        tasks = await task_service.get_tasks(limit=50)

        # Analyze with AI
        analysis = await chat_service.analyze_task_priorities(tasks)

        logger.info("Task priority analysis completed", task_count=len(tasks))
        return analysis

    except Exception as e:
        logger.error("Failed to analyze task priorities", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to analyze priorities")


@router.post("/generate-report")
async def generate_executive_report(
    report_request: ReportRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Generate executive reports with AI insights.

    Creates comprehensive executive reports including task performance,
    strategic analysis, and business intelligence.
    """
    try:
        task_service = TaskService(db)

        if report_request.report_type == "daily_summary":
            # Generate daily executive summary
            daily_summary = await task_service.get_daily_summary()
            report = await report_generator.generate_daily_executive_summary(daily_summary)

        elif report_request.report_type == "task_performance":
            # Generate task performance report
            tasks = await task_service.get_tasks(limit=100)
            report = await report_generator.generate_task_performance_report(
                tasks, report_request.time_period
            )

        elif report_request.report_type == "strategic_dashboard":
            # Generate strategic dashboard report
            daily_summary = await task_service.get_daily_summary()
            kpi_data = {"revenue_growth": 12, "customer_satisfaction": 4.2}  # Placeholder
            market_data = {"market_trend": "positive"}  # Placeholder
            task_data = daily_summary.stats.dict()

            report = await report_generator.generate_strategic_dashboard_report(
                kpi_data, market_data, task_data
            )

        else:
            raise HTTPException(status_code=400, detail="Invalid report type")

        logger.info("Executive report generated", report_type=report_request.report_type)

        return {
            "report": {
                "title": report.title,
                "generated_at": report.generated_at.isoformat(),
                "type": report.report_type,
                "summary": report.summary,
                "content": report.content,
                "recommendations": report.recommendations,
                "next_actions": report.next_actions
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to generate executive report", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to generate report")
