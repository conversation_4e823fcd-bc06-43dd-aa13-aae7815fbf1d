"""
Main API router for Rynne AI CEO Agent v1.

This module aggregates all API endpoints and provides the main router
for the FastAPI application.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import tasks, strategic, scenarios, chat

# Create main API router
api_router = APIRouter()

# Include endpoint routers
api_router.include_router(
    tasks.router,
    prefix="/tasks",
    tags=["Task Management"]
)

api_router.include_router(
    strategic.router,
    prefix="/strategic",
    tags=["Strategic Analysis"]
)

api_router.include_router(
    scenarios.router,
    prefix="/scenarios",
    tags=["Scenario Simulation"]
)

api_router.include_router(
    chat.router,
    prefix="/chat",
    tags=["AI Communication"]
)
