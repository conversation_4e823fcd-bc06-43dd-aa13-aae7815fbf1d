"""
Task Service for <PERSON>ynne AI CEO Agent.

This service implements the core task management logic including:
- Eisenhower Matrix prioritization
- Intelligent task assignment
- Daily summary generation
- AI-powered insights
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
import structlog

from app.models.task import Task, TaskPriority, TaskStatus
from app.schemas.task import (
    TaskCreate, TaskUpdate, TaskResponse, TaskPriorityMatrix,
    DailyTaskSummary, TaskSummaryStats
)

logger = structlog.get_logger()


class TaskService:
    """Service class for task management operations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_task(self, task_data: TaskCreate) -> TaskResponse:
        """
        Create a new task with automatic prioritization.
        
        Args:
            task_data: Task creation data
            
        Returns:
            TaskResponse: Created task with priority analysis
        """
        # Create task instance
        task = Task(
            title=task_data.title,
            description=task_data.description,
            urgency=task_data.urgency.value,
            importance=task_data.importance.value,
            assignee=task_data.assignee,
            assignee_type=task_data.assignee_type,
            estimated_effort=task_data.estimated_effort,
            deadline=task_data.deadline,
            business_impact=task_data.business_impact,
            revenue_impact=task_data.revenue_impact,
            cost_impact=task_data.cost_impact,
            tags=task_data.tags,
            dependencies=task_data.dependencies,
        )
        
        # Calculate priority and category
        task.update_priority()
        
        # Set confidence score (placeholder for AI analysis)
        task.confidence_score = 0.8
        
        # Save to database
        self.db.add(task)
        await self.db.commit()
        await self.db.refresh(task)
        
        logger.info(
            "Task created successfully",
            task_id=task.id,
            title=task.title,
            priority=task.priority_category,
            score=task.priority_score
        )
        
        return TaskResponse.from_orm(task)
    
    async def get_tasks(
        self,
        skip: int = 0,
        limit: int = 100,
        priority: Optional[str] = None,
        status: Optional[str] = None,
        assignee: Optional[str] = None
    ) -> List[TaskResponse]:
        """
        Retrieve tasks with optional filtering.
        
        Args:
            skip: Number of tasks to skip
            limit: Maximum number of tasks to return
            priority: Filter by priority category
            status: Filter by task status
            assignee: Filter by assignee
            
        Returns:
            List[TaskResponse]: Filtered tasks
        """
        query = select(Task)
        
        # Apply filters
        filters = []
        if priority:
            filters.append(Task.priority_category == priority)
        if status:
            filters.append(Task.status == status)
        if assignee:
            filters.append(Task.assignee.ilike(f"%{assignee}%"))
        
        if filters:
            query = query.where(and_(*filters))
        
        # Order by priority score (descending) and creation date
        query = query.order_by(Task.priority_score.desc(), Task.created_at.desc())
        query = query.offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        tasks = result.scalars().all()
        
        return [TaskResponse.from_orm(task) for task in tasks]
    
    async def get_task(self, task_id: int) -> Optional[TaskResponse]:
        """
        Get a specific task by ID.
        
        Args:
            task_id: Task ID
            
        Returns:
            TaskResponse: Task data or None if not found
        """
        query = select(Task).where(Task.id == task_id)
        result = await self.db.execute(query)
        task = result.scalar_one_or_none()
        
        if task:
            return TaskResponse.from_orm(task)
        return None
    
    async def update_task(self, task_id: int, task_update: TaskUpdate) -> Optional[TaskResponse]:
        """
        Update a task with automatic re-prioritization.
        
        Args:
            task_id: Task ID
            task_update: Update data
            
        Returns:
            TaskResponse: Updated task or None if not found
        """
        query = select(Task).where(Task.id == task_id)
        result = await self.db.execute(query)
        task = result.scalar_one_or_none()
        
        if not task:
            return None
        
        # Update fields
        update_data = task_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(task, field):
                setattr(task, field, value)
        
        # Handle status changes
        if task_update.status == TaskStatus.COMPLETED and not task.completed_at:
            task.completed_at = datetime.utcnow()
        elif task_update.status != TaskStatus.COMPLETED and task.completed_at:
            task.completed_at = None
        
        # Recalculate priority if urgency or importance changed
        if task_update.urgency or task_update.importance:
            task.update_priority()
        
        await self.db.commit()
        await self.db.refresh(task)
        
        logger.info(
            "Task updated successfully",
            task_id=task_id,
            priority=task.priority_category,
            status=task.status
        )
        
        return TaskResponse.from_orm(task)
    
    async def delete_task(self, task_id: int) -> bool:
        """
        Delete a task.
        
        Args:
            task_id: Task ID
            
        Returns:
            bool: True if deleted, False if not found
        """
        query = select(Task).where(Task.id == task_id)
        result = await self.db.execute(query)
        task = result.scalar_one_or_none()
        
        if not task:
            return False
        
        await self.db.delete(task)
        await self.db.commit()
        
        logger.info("Task deleted successfully", task_id=task_id)
        return True

    async def get_priority_matrix(self) -> TaskPriorityMatrix:
        """
        Get tasks organized by Eisenhower Matrix categories.

        Returns:
            TaskPriorityMatrix: Tasks categorized by priority
        """
        # Get all active tasks
        query = select(Task).where(Task.status.in_([TaskStatus.PENDING, TaskStatus.IN_PROGRESS]))
        result = await self.db.execute(query)
        tasks = result.scalars().all()

        # Organize by priority category
        matrix = {
            TaskPriority.DO_FIRST.value: [],
            TaskPriority.SCHEDULE.value: [],
            TaskPriority.DELEGATE.value: [],
            TaskPriority.DONT_DO.value: []
        }

        for task in tasks:
            task_response = TaskResponse.from_orm(task)
            matrix[task.priority_category].append(task_response)

        # Sort each category by priority score
        for category in matrix:
            matrix[category].sort(key=lambda x: x.priority_score, reverse=True)

        return TaskPriorityMatrix(
            do_first=matrix[TaskPriority.DO_FIRST.value],
            schedule=matrix[TaskPriority.SCHEDULE.value],
            delegate=matrix[TaskPriority.DELEGATE.value],
            dont_do=matrix[TaskPriority.DONT_DO.value]
        )

    async def get_daily_summary(self) -> DailyTaskSummary:
        """
        Generate comprehensive daily task summary.

        Returns:
            DailyTaskSummary: Executive-level task overview
        """
        today = datetime.utcnow().date()

        # Get task statistics
        stats = await self._get_task_stats()

        # Get priority matrix
        matrix = await self.get_priority_matrix()

        # Generate AI insights
        insights = await self._generate_insights(stats, matrix)

        return DailyTaskSummary(
            date=datetime.utcnow(),
            stats=stats,
            priority_matrix=matrix,
            key_insights=insights['insights'],
            recommended_actions=insights['actions'],
            focus_areas=insights['focus_areas']
        )

    async def assign_task(
        self,
        task_id: int,
        assignee: str,
        deadline: Optional[str] = None
    ) -> Optional[TaskResponse]:
        """
        Assign a task to a team member or team.

        Args:
            task_id: Task ID
            assignee: Person or team to assign to
            deadline: Optional deadline for assignment

        Returns:
            TaskResponse: Updated task or None if not found
        """
        query = select(Task).where(Task.id == task_id)
        result = await self.db.execute(query)
        task = result.scalar_one_or_none()

        if not task:
            return None

        # Update assignment
        task.assignee = assignee
        if deadline:
            task.deadline = datetime.fromisoformat(deadline.replace('Z', '+00:00'))

        # Update status if currently pending
        if task.status == TaskStatus.PENDING:
            task.status = TaskStatus.IN_PROGRESS

        task.updated_at = datetime.utcnow()

        await self.db.commit()
        await self.db.refresh(task)

        logger.info(
            "Task assigned successfully",
            task_id=task_id,
            assignee=assignee,
            deadline=deadline
        )

        return TaskResponse.from_orm(task)

    async def _get_task_stats(self) -> TaskSummaryStats:
        """Get task summary statistics."""
        # Total tasks
        total_query = select(func.count(Task.id))
        total_result = await self.db.execute(total_query)
        total_tasks = total_result.scalar()

        # Tasks by status
        status_query = select(Task.status, func.count(Task.id)).group_by(Task.status)
        status_result = await self.db.execute(status_query)
        by_status = {row[0]: row[1] for row in status_result}

        # Tasks by priority
        priority_query = select(Task.priority_category, func.count(Task.id)).group_by(Task.priority_category)
        priority_result = await self.db.execute(priority_query)
        by_priority = {row[0]: row[1] for row in priority_result}

        # Overdue tasks
        overdue_query = select(func.count(Task.id)).where(
            and_(
                Task.deadline < datetime.utcnow(),
                Task.status.in_([TaskStatus.PENDING, TaskStatus.IN_PROGRESS])
            )
        )
        overdue_result = await self.db.execute(overdue_query)
        overdue_count = overdue_result.scalar()

        # Completed today
        today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        completed_today_query = select(func.count(Task.id)).where(
            and_(
                Task.completed_at >= today_start,
                Task.status == TaskStatus.COMPLETED
            )
        )
        completed_today_result = await self.db.execute(completed_today_query)
        completed_today = completed_today_result.scalar()

        # Effort statistics
        effort_query = select(
            func.sum(Task.estimated_effort),
            func.sum(Task.actual_effort)
        ).where(Task.status != TaskStatus.CANCELLED)
        effort_result = await self.db.execute(effort_query)
        effort_row = effort_result.first()

        return TaskSummaryStats(
            total_tasks=total_tasks or 0,
            by_status=by_status,
            by_priority=by_priority,
            overdue_count=overdue_count or 0,
            completed_today=completed_today or 0,
            avg_completion_time=None,  # TODO: Calculate from historical data
            total_estimated_effort=effort_row[0] or 0.0,
            total_actual_effort=effort_row[1] or 0.0
        )

    async def _generate_insights(self, stats: TaskSummaryStats, matrix: TaskPriorityMatrix) -> Dict[str, List[str]]:
        """Generate AI-powered insights from task data."""
        insights = []
        actions = []
        focus_areas = []

        # Analyze overdue tasks
        if stats.overdue_count > 0:
            insights.append(f"{stats.overdue_count} tasks are overdue and require immediate attention")
            actions.append("Review and prioritize overdue tasks")
            focus_areas.append("Overdue task resolution")

        # Analyze DO_FIRST category
        do_first_count = len(matrix.do_first)
        if do_first_count > 5:
            insights.append(f"{do_first_count} critical tasks in DO_FIRST category may indicate resource constraints")
            actions.append("Consider delegating or deferring lower-priority items")
            focus_areas.append("Resource allocation optimization")

        # Analyze completion rate
        if stats.completed_today > 0:
            insights.append(f"Team completed {stats.completed_today} tasks today, showing good productivity")

        # Analyze effort tracking
        if stats.total_estimated_effort > 0 and stats.total_actual_effort > 0:
            efficiency = (stats.total_estimated_effort / stats.total_actual_effort) * 100
            if efficiency < 80:
                insights.append("Tasks are taking longer than estimated, consider reviewing estimation process")
                actions.append("Analyze estimation accuracy and adjust planning")

        return {
            'insights': insights,
            'actions': actions,
            'focus_areas': focus_areas
        }
