"""
Data Integration Service for <PERSON><PERSON>e AI CEO Agent.

This service coordinates data collection from various sources,
processes the data, and integrates it into the system.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import asyncio
import structlog

from app.integrations.base import DataIntegrationManager
from app.integrations.task_connectors import <PERSON>raConnector, SlackConnector, EmailConnector
from app.integrations.market_connectors import AlphaVantageConnector, NewsAPIConnector
from app.processors.task_processors import JiraTaskProcessor, SlackTaskProcessor
from app.services.task_service import TaskService
from app.schemas.task import TaskCreate
from app.models.task import TaskUrgency, TaskImportance
from app.core.config import settings

logger = structlog.get_logger()


class IntegrationService:
    """
    Service for managing data integrations and automated task creation.
    
    This service orchestrates the collection of data from various sources,
    processes it through appropriate processors, and creates tasks automatically.
    """
    
    def __init__(self):
        self.integration_manager = DataIntegrationManager()
        self.logger = structlog.get_logger("IntegrationService")
        self._setup_integrations()
    
    def _setup_integrations(self):
        """Setup and register all data connectors and processors."""
        
        # Task Management Connectors
        if settings.JIRA_API_TOKEN:
            jira_config = {
                'base_url': 'https://your-domain.atlassian.net',  # Configure as needed
                'username': '<EMAIL>',  # Configure as needed
                'api_token': settings.JIRA_API_TOKEN
            }
            jira_connector = JiraConnector(jira_config)
            self.integration_manager.register_connector('jira', jira_connector)
            
            jira_processor = JiraTaskProcessor({})
            self.integration_manager.register_processor('jira_tasks', jira_processor)
        
        if settings.SLACK_BOT_TOKEN:
            slack_config = {
                'bot_token': settings.SLACK_BOT_TOKEN
            }
            slack_connector = SlackConnector(slack_config)
            self.integration_manager.register_connector('slack', slack_connector)
            
            slack_processor = SlackTaskProcessor({})
            self.integration_manager.register_processor('slack_tasks', slack_processor)
        
        # Market Data Connectors
        if settings.ALPHA_VANTAGE_API_KEY:
            av_config = {
                'api_key': settings.ALPHA_VANTAGE_API_KEY
            }
            av_connector = AlphaVantageConnector(av_config)
            self.integration_manager.register_connector('alpha_vantage', av_connector)
        
        if settings.NEWS_API_KEY:
            news_config = {
                'api_key': settings.NEWS_API_KEY
            }
            news_connector = NewsAPIConnector(news_config)
            self.integration_manager.register_connector('news_api', news_connector)
        
        self.logger.info("Data integrations setup completed")
    
    async def sync_tasks_from_jira(self, task_service: TaskService) -> Dict[str, Any]:
        """
        Sync tasks from Jira and create them in the system.
        
        Args:
            task_service: Task service instance
            
        Returns:
            Dict[str, Any]: Sync results
        """
        if 'jira' not in self.integration_manager.connectors:
            return {'error': 'Jira integration not configured'}
        
        try:
            # Fetch and process Jira issues
            query = {
                'jql': 'assignee = currentUser() AND resolution = Unresolved ORDER BY priority DESC',
                'fields': ['summary', 'description', 'priority', 'status', 'assignee', 'duedate', 'issuetype', 'labels'],
                'max_results': 50
            }
            
            processed_tasks = await self.integration_manager.fetch_and_process(
                'jira', 'jira_tasks', query
            )
            
            # Create tasks in the system
            created_tasks = []
            for task_data in processed_tasks:
                try:
                    # Convert to TaskCreate schema
                    task_create = TaskCreate(
                        title=task_data['title'],
                        description=task_data['description'],
                        urgency=TaskUrgency(task_data['urgency']),
                        importance=TaskImportance(task_data['importance']),
                        assignee=task_data.get('assignee'),
                        estimated_effort=task_data.get('estimated_effort'),
                        deadline=task_data.get('deadline'),
                        business_impact=task_data.get('business_impact'),
                        tags=task_data.get('tags', [])
                    )
                    
                    # Check if task already exists (by source_id)
                    existing_task = await self._find_existing_task(
                        task_service, 'jira', task_data['source_id']
                    )
                    
                    if not existing_task:
                        created_task = await task_service.create_task(task_create)
                        created_tasks.append(created_task)
                    
                except Exception as e:
                    self.logger.error("Failed to create task from Jira", error=str(e), task=task_data['title'])
            
            self.logger.info(
                "Jira sync completed",
                fetched=len(processed_tasks),
                created=len(created_tasks)
            )
            
            return {
                'source': 'jira',
                'fetched_count': len(processed_tasks),
                'created_count': len(created_tasks),
                'created_tasks': [task.dict() for task in created_tasks]
            }
            
        except Exception as e:
            self.logger.error("Jira sync failed", error=str(e))
            return {'error': str(e)}
    
    async def sync_tasks_from_slack(self, task_service: TaskService, channel_id: str = None) -> Dict[str, Any]:
        """
        Sync tasks from Slack messages and create them in the system.
        
        Args:
            task_service: Task service instance
            channel_id: Specific channel to sync from
            
        Returns:
            Dict[str, Any]: Sync results
        """
        if 'slack' not in self.integration_manager.connectors:
            return {'error': 'Slack integration not configured'}
        
        try:
            # Fetch recent messages
            query = {
                'endpoint': 'conversations.history',
                'channel': channel_id,
                'limit': 100
            }
            
            processed_tasks = await self.integration_manager.fetch_and_process(
                'slack', 'slack_tasks', query
            )
            
            # Create tasks in the system
            created_tasks = []
            for task_data in processed_tasks:
                try:
                    task_create = TaskCreate(
                        title=task_data['title'],
                        description=task_data['description'],
                        urgency=TaskUrgency(task_data['urgency']),
                        importance=TaskImportance(task_data['importance']),
                        estimated_effort=task_data.get('estimated_effort', 1.0),
                        business_impact=task_data.get('business_impact'),
                        tags=task_data.get('tags', [])
                    )
                    
                    # Check if task already exists
                    existing_task = await self._find_existing_task(
                        task_service, 'slack', task_data['source_id']
                    )
                    
                    if not existing_task:
                        created_task = await task_service.create_task(task_create)
                        created_tasks.append(created_task)
                    
                except Exception as e:
                    self.logger.error("Failed to create task from Slack", error=str(e))
            
            self.logger.info(
                "Slack sync completed",
                fetched=len(processed_tasks),
                created=len(created_tasks)
            )
            
            return {
                'source': 'slack',
                'fetched_count': len(processed_tasks),
                'created_count': len(created_tasks),
                'created_tasks': [task.dict() for task in created_tasks]
            }
            
        except Exception as e:
            self.logger.error("Slack sync failed", error=str(e))
            return {'error': str(e)}
    
    async def fetch_market_intelligence(self) -> Dict[str, Any]:
        """
        Fetch market intelligence data for strategic analysis.
        
        Returns:
            Dict[str, Any]: Market intelligence data
        """
        intelligence_data = {}
        
        # Fetch financial data
        if 'alpha_vantage' in self.integration_manager.connectors:
            try:
                financial_query = {
                    'function': 'GLOBAL_QUOTE',
                    'symbol': 'SPY'  # S&P 500 ETF as market indicator
                }
                
                financial_data = await self.integration_manager.fetch_and_process(
                    'alpha_vantage', None, financial_query
                )
                intelligence_data['market_data'] = financial_data
                
            except Exception as e:
                self.logger.error("Failed to fetch financial data", error=str(e))
        
        # Fetch news data
        if 'news_api' in self.integration_manager.connectors:
            try:
                news_query = {
                    'q': 'business OR economy OR market',
                    'sortBy': 'publishedAt',
                    'pageSize': 20,
                    'from_date': (datetime.utcnow() - timedelta(days=1)).isoformat()
                }
                
                news_data = await self.integration_manager.fetch_and_process(
                    'news_api', None, news_query
                )
                intelligence_data['news_data'] = news_data
                
            except Exception as e:
                self.logger.error("Failed to fetch news data", error=str(e))
        
        return intelligence_data
    
    async def run_scheduled_sync(self, task_service: TaskService) -> Dict[str, Any]:
        """
        Run scheduled synchronization of all data sources.
        
        Args:
            task_service: Task service instance
            
        Returns:
            Dict[str, Any]: Sync results from all sources
        """
        results = {
            'timestamp': datetime.utcnow().isoformat(),
            'sources': {}
        }
        
        # Sync tasks from all available sources
        sync_tasks = []
        
        if 'jira' in self.integration_manager.connectors:
            sync_tasks.append(self.sync_tasks_from_jira(task_service))
        
        if 'slack' in self.integration_manager.connectors:
            sync_tasks.append(self.sync_tasks_from_slack(task_service))
        
        # Run all syncs concurrently
        if sync_tasks:
            sync_results = await asyncio.gather(*sync_tasks, return_exceptions=True)
            
            for i, result in enumerate(sync_results):
                if isinstance(result, Exception):
                    self.logger.error("Sync task failed", error=str(result))
                else:
                    source = result.get('source', f'source_{i}')
                    results['sources'][source] = result
        
        # Fetch market intelligence
        try:
            market_data = await self.fetch_market_intelligence()
            results['market_intelligence'] = market_data
        except Exception as e:
            self.logger.error("Market intelligence fetch failed", error=str(e))
            results['market_intelligence'] = {'error': str(e)}
        
        self.logger.info("Scheduled sync completed", results=results)
        return results
    
    async def _find_existing_task(self, task_service: TaskService, source: str, source_id: str) -> Optional[Any]:
        """Find existing task by source and source_id."""
        # This would need to be implemented based on how we store source metadata
        # For now, return None to allow all tasks to be created
        return None
    
    async def get_integration_status(self) -> Dict[str, Any]:
        """
        Get status of all integrations.
        
        Returns:
            Dict[str, Any]: Integration health status
        """
        health_status = await self.integration_manager.health_check()
        
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'connectors': health_status,
            'total_connectors': len(self.integration_manager.connectors),
            'healthy_connectors': sum(1 for status in health_status.values() if status),
            'processors': list(self.integration_manager.processors.keys())
        }
