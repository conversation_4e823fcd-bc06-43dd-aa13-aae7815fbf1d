"""
Tests for Task Service functionality.

This module tests the core task management logic including
Eisenhower Matrix prioritization and task operations.
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.task_service import TaskService
from app.schemas.task import TaskCreate, TaskUpdate
from app.models.task import TaskUrgency, TaskImportance, TaskStatus


class TestTaskService:
    """Test suite for TaskService."""
    
    @pytest.fixture
    async def task_service(self, db_session: AsyncSession):
        """Create a TaskService instance for testing."""
        return TaskService(db_session)
    
    @pytest.fixture
    def sample_task_data(self):
        """Sample task data for testing."""
        return TaskCreate(
            title="Test Task",
            description="This is a test task",
            urgency=TaskUrgency.HIGH,
            importance=TaskImportance.HIGH,
            assignee="Test User",
            estimated_effort=4.0,
            deadline=datetime.utcnow() + timedelta(days=7),
            business_impact="Test business impact",
            tags=["test", "sample"]
        )
    
    async def test_create_task(self, task_service: TaskService, sample_task_data: TaskCreate):
        """Test task creation with automatic prioritization."""
        # Create task
        created_task = await task_service.create_task(sample_task_data)
        
        # Verify task was created correctly
        assert created_task.title == sample_task_data.title
        assert created_task.description == sample_task_data.description
        assert created_task.urgency == sample_task_data.urgency.value
        assert created_task.importance == sample_task_data.importance.value
        assert created_task.assignee == sample_task_data.assignee
        
        # Verify priority calculation
        assert created_task.priority_category == "DO_FIRST"  # High urgency + High importance
        assert created_task.priority_score > 0
        assert created_task.confidence_score is not None
    
    async def test_get_tasks(self, task_service: TaskService, sample_task_data: TaskCreate):
        """Test retrieving tasks with filtering."""
        # Create a test task
        await task_service.create_task(sample_task_data)
        
        # Get all tasks
        tasks = await task_service.get_tasks()
        assert len(tasks) >= 1
        
        # Test filtering by priority
        priority_tasks = await task_service.get_tasks(priority="DO_FIRST")
        assert len(priority_tasks) >= 1
        assert all(task.priority_category == "DO_FIRST" for task in priority_tasks)
        
        # Test filtering by assignee
        assignee_tasks = await task_service.get_tasks(assignee="Test User")
        assert len(assignee_tasks) >= 1
        assert all("Test User" in (task.assignee or "") for task in assignee_tasks)
    
    async def test_update_task(self, task_service: TaskService, sample_task_data: TaskCreate):
        """Test task updates with re-prioritization."""
        # Create task
        created_task = await task_service.create_task(sample_task_data)
        
        # Update task
        update_data = TaskUpdate(
            title="Updated Test Task",
            urgency=TaskUrgency.LOW,
            importance=TaskImportance.LOW,
            status=TaskStatus.IN_PROGRESS
        )
        
        updated_task = await task_service.update_task(created_task.id, update_data)
        
        # Verify updates
        assert updated_task is not None
        assert updated_task.title == "Updated Test Task"
        assert updated_task.urgency == TaskUrgency.LOW.value
        assert updated_task.importance == TaskImportance.LOW.value
        assert updated_task.status == TaskStatus.IN_PROGRESS.value
        
        # Verify priority recalculation
        assert updated_task.priority_category == "DONT_DO"  # Low urgency + Low importance
    
    async def test_delete_task(self, task_service: TaskService, sample_task_data: TaskCreate):
        """Test task deletion."""
        # Create task
        created_task = await task_service.create_task(sample_task_data)
        
        # Delete task
        success = await task_service.delete_task(created_task.id)
        assert success is True
        
        # Verify task is deleted
        deleted_task = await task_service.get_task(created_task.id)
        assert deleted_task is None
    
    async def test_priority_matrix(self, task_service: TaskService):
        """Test Eisenhower Matrix organization."""
        # Create tasks with different priorities
        tasks_data = [
            TaskCreate(
                title="Critical Bug Fix",
                urgency=TaskUrgency.CRITICAL,
                importance=TaskImportance.CRITICAL,
                description="Fix critical production bug"
            ),
            TaskCreate(
                title="Strategic Planning",
                urgency=TaskUrgency.LOW,
                importance=TaskImportance.HIGH,
                description="Plan next quarter strategy"
            ),
            TaskCreate(
                title="Email Response",
                urgency=TaskUrgency.HIGH,
                importance=TaskImportance.LOW,
                description="Respond to routine emails"
            ),
            TaskCreate(
                title="Social Media Check",
                urgency=TaskUrgency.LOW,
                importance=TaskImportance.LOW,
                description="Check social media updates"
            )
        ]
        
        # Create all tasks
        for task_data in tasks_data:
            await task_service.create_task(task_data)
        
        # Get priority matrix
        matrix = await task_service.get_priority_matrix()
        
        # Verify matrix structure
        assert len(matrix.do_first) >= 1  # Critical bug fix
        assert len(matrix.schedule) >= 1  # Strategic planning
        assert len(matrix.delegate) >= 1  # Email response
        assert len(matrix.dont_do) >= 1  # Social media check
        
        # Verify tasks are in correct categories
        do_first_titles = [task.title for task in matrix.do_first]
        assert "Critical Bug Fix" in do_first_titles
        
        schedule_titles = [task.title for task in matrix.schedule]
        assert "Strategic Planning" in schedule_titles
    
    async def test_daily_summary(self, task_service: TaskService, sample_task_data: TaskCreate):
        """Test daily summary generation."""
        # Create some test tasks
        await task_service.create_task(sample_task_data)
        
        # Create completed task
        completed_task_data = TaskCreate(
            title="Completed Task",
            urgency=TaskUrgency.MEDIUM,
            importance=TaskImportance.MEDIUM,
            description="This task is completed"
        )
        completed_task = await task_service.create_task(completed_task_data)
        
        # Mark as completed
        await task_service.update_task(
            completed_task.id,
            TaskUpdate(status=TaskStatus.COMPLETED)
        )
        
        # Get daily summary
        summary = await task_service.get_daily_summary()
        
        # Verify summary structure
        assert summary.stats.total_tasks >= 2
        assert summary.stats.by_status.get('completed', 0) >= 1
        assert len(summary.key_insights) >= 0
        assert len(summary.recommended_actions) >= 0
        assert len(summary.focus_areas) >= 0
        
        # Verify priority matrix is included
        assert summary.priority_matrix is not None
    
    async def test_assign_task(self, task_service: TaskService, sample_task_data: TaskCreate):
        """Test task assignment functionality."""
        # Create task
        created_task = await task_service.create_task(sample_task_data)
        
        # Assign task
        assigned_task = await task_service.assign_task(
            created_task.id,
            "New Assignee",
            (datetime.utcnow() + timedelta(days=3)).isoformat()
        )
        
        # Verify assignment
        assert assigned_task is not None
        assert assigned_task.assignee == "New Assignee"
        assert assigned_task.status == TaskStatus.IN_PROGRESS.value
        assert assigned_task.deadline is not None
    
    async def test_task_priority_calculation(self):
        """Test priority score calculation logic."""
        from app.models.task import Task
        
        # Create task with high urgency and importance
        high_priority_task = Task(
            title="High Priority Task",
            urgency=TaskUrgency.CRITICAL.value,
            importance=TaskImportance.CRITICAL.value,
            deadline=datetime.utcnow() + timedelta(days=1),  # Due soon
            revenue_impact=50000  # High revenue impact
        )
        
        high_priority_task.update_priority()
        
        # Create task with low urgency and importance
        low_priority_task = Task(
            title="Low Priority Task",
            urgency=TaskUrgency.LOW.value,
            importance=TaskImportance.LOW.value,
            deadline=datetime.utcnow() + timedelta(days=30)  # Due later
        )
        
        low_priority_task.update_priority()
        
        # Verify priority scores
        assert high_priority_task.priority_score > low_priority_task.priority_score
        assert high_priority_task.priority_category == "DO_FIRST"
        assert low_priority_task.priority_category == "DONT_DO"
    
    async def test_overdue_task_detection(self, task_service: TaskService):
        """Test overdue task detection."""
        # Create overdue task
        overdue_task_data = TaskCreate(
            title="Overdue Task",
            urgency=TaskUrgency.HIGH,
            importance=TaskImportance.HIGH,
            deadline=datetime.utcnow() - timedelta(days=1)  # Yesterday
        )
        
        created_task = await task_service.create_task(overdue_task_data)
        
        # Verify overdue detection
        assert created_task.is_overdue is True
        assert created_task.days_until_deadline is not None
        assert created_task.days_until_deadline < 0
