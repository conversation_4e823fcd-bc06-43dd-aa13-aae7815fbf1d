"""
Pytest configuration and fixtures for Rynne AI CEO Agent tests.

This module provides common test fixtures and configuration
for the test suite.
"""

import pytest
import asyncio
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool

from app.core.database import Base
from app.core.config import settings


# Test database URL (in-memory SQLite for testing)
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_engine():
    """Create test database engine."""
    engine = create_async_engine(
        TEST_DATABASE_URL,
        echo=False,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False}
    )
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # Clean up
    await engine.dispose()


@pytest.fixture
async def db_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create a database session for testing."""
    async_session = async_sessionmaker(
        test_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
        await session.rollback()


@pytest.fixture
def mock_openai_response():
    """Mock OpenAI API response for testing."""
    return {
        "choices": [
            {
                "message": {
                    "content": "This is a mock AI response for testing purposes."
                }
            }
        ]
    }


@pytest.fixture
def sample_task_data():
    """Sample task data for testing."""
    return {
        "title": "Sample Test Task",
        "description": "This is a sample task for testing",
        "urgency": "high",
        "importance": "high",
        "assignee": "Test User",
        "estimated_effort": 2.0,
        "business_impact": "Test impact",
        "tags": ["test", "sample"]
    }


@pytest.fixture
def sample_integration_data():
    """Sample integration data for testing."""
    return {
        "jira_issue": {
            "key": "TEST-123",
            "fields": {
                "summary": "Test Jira Issue",
                "description": "Test description",
                "priority": {"name": "High"},
                "status": {"name": "In Progress"},
                "assignee": {"displayName": "Test User"},
                "issuetype": {"name": "Bug"},
                "labels": ["urgent", "customer"]
            }
        },
        "slack_message": {
            "text": "TODO: Fix the critical bug in production",
            "user": "U123456",
            "ts": "1234567890.123456"
        }
    }


# Mock settings for testing
@pytest.fixture(autouse=True)
def mock_settings(monkeypatch):
    """Mock settings for testing."""
    monkeypatch.setattr(settings, "OPENAI_API_KEY", "test-key")
    monkeypatch.setattr(settings, "DATABASE_URL", TEST_DATABASE_URL)
    monkeypatch.setattr(settings, "ENVIRONMENT", "testing")
